import os
from fastapi import <PERSON>AP<PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import httpx

load_dotenv()

OLLAMA_URL = os.getenv("OLLAMA_URL", "http://localhost:11434/v1/chat/completions")
DEFAULT_MODEL = os.getenv("DEFAULT_MODEL", "qwen3:4b")

app = FastAPI()

# CORS 支持
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 可根据需要调整
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/api/chat")
async def chat_endpoint(request: Request):
    body = await request.json()
    messages = body.get("messages")
    model = body.get("model", DEFAULT_MODEL)

    # 日志
    print("收到请求:", body)

    if not messages or not isinstance(messages, list):
        print("messages 字段缺失或格式错误")
        raise HTTPException(status_code=400, detail={
            "error": {
                "message": "messages 字段缺失或格式错误",
                "type": "invalid_request"
            }
        })

    payload = {
        "model": model,
        "messages": messages
    }

    print("转发到Ollama:", payload)

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(OLLAMA_URL, json=payload)
            data = response.json()
        print("Ollama响应:", data.get("choices", data))
        return JSONResponse(content=data)
    except Exception as e:
        print("Ollama请求出错:", e)
        raise HTTPException(status_code=500, detail={
            "error": {
                "message": str(e) or "Ollama服务异常",
                "type": "internal_error"
            }
        }) 
