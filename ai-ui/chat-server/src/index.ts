import express from 'express';
import cors from 'cors';
import fetch from 'node-fetch';
import path from 'path';

const app = express();
const port = 3000;
const OLLAMA_URL = 'http://localhost:11434/v1/chat/completions';
const DEFAULT_MODEL = 'qwen3:4b';

app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

app.post('/api/chat', async (req, res) => {
    const { messages, model } = req.body;
    // 日志：收到的请求体
    console.log('收到请求:', JSON.stringify(req.body));

    if (!messages || !Array.isArray(messages)) {
        console.error('messages 字段缺失或格式错误');
        return res.status(400).json({
            error: {
                message: 'messages 字段缺失或格式错误',
                type: 'invalid_request',
            },
        });
    }

    const payload = {
        model: model || DEFAULT_MODEL,
        messages,
    };

    // 日志：转发的请求体
    console.log('转发到Ollama:', JSON.stringify(payload));

    try {
        const ollamaRes = await fetch(OLLAMA_URL, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload),
        });
        const data = await ollamaRes.json();
        // 日志：Ollama响应主要内容
        console.log('Ollama响应:', JSON.stringify(data.choices || data));
        res.json(data);
    } catch (error) {
        console.error('Ollama请求出错:', error);
        const errMsg =
            error && typeof error === 'object' && 'message' in error
                ? (error as any).message
                : 'Ollama服务异常';
        res.status(500).json({
            error: {
                message: errMsg,
                type: 'internal_error',
            },
        });
    }
});

app.listen(port, () => {
    console.log(`Chat server listening at http://localhost:${port}`);
});
