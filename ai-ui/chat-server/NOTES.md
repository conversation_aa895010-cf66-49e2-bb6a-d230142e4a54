# Workspace 目录规范与注意事项

本项目为多项目（monorepo）结构，包含多个子项目目录。请严格遵守以下目录归属规范：

## 目录结构示例

```
workspace-root/
  ├── ai-chrome-chat/      # 前端相关代码，仅限前端开发使用
  └── chat-server/        # Python 后端服务，仅限后端开发使用
```

## 规范与注意事项

1. **chat-server/**
   - 仅存放 Python 后端相关代码（如 FastAPI 服务、.env、pyproject.toml、测试用例等）。
   - 不要在 ai-chrome-chat/ 目录下生成或修改任何与 chat-server 相关的文件。
   - 主代码建议放在 `chat-server/services/`，测试用例放在 `chat-server/tests/`。

2. **ai-chrome-chat/**
   - 仅存放前端相关代码（如 React/Vite/Chrome 插件等）。
   - 不要在 chat-server/ 目录下生成或修改任何与前端相关的文件。

3. **通用建议**
   - 生成、修改、删除文件时，务必确认操作的目标目录，避免跨项目误操作。
   - 如有新的子项目，需在本文件补充说明。

---

> 如有疑问或需调整目录规范，请在本文件补充说明并通知团队成员。 