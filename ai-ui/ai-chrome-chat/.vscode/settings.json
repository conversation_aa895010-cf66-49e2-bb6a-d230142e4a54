{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "files.eol": "\n", "files.insertFinalNewline": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "explorer.excludeGitIgnore": false, "eslint.validate": ["typescript", "typescriptreact"], "prettier.requireConfig": true, "tailwindCSS.experimental.classRegex": ["tw`([^`]*)", "tw=\"([^\"]*)", "tw={\"([^\"}]*)", "tw.\\w+`([^`]*)"]}