{"version": "0.2.0", "configurations": [{"type": "chrome", "request": "launch", "name": "Launch Chrome with Extension", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/src", "runtimeArgs": ["--disable-extensions-except=${workspaceFolder}/dist", "--load-extension=${workspaceFolder}/dist"]}, {"type": "chrome", "request": "launch", "name": "Extension (Temp Profile)", "url": "chrome://extensions", "webRoot": "${workspaceFolder}/src", "runtimeArgs": ["--disable-extensions-except=${workspaceFolder}/dist", "--load-extension=${workspaceFolder}/dist", "--user-data-dir=${workspaceFolder}/.vscode/chrome-user-data"], "preLaunchTask": "clean chrome temp user data"}]}