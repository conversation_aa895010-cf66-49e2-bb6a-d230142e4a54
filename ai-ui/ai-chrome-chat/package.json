{"name": "ai-chrome-chat", "version": "1.0.0", "description": "Chrome extension with Preact, TailwindCSS, Vite, TypeScript, AI chat widget", "scripts": {"dev": "vite", "build": "vite build && cp manifest.json dist/ && cp -R icons dist/", "build:open": "npm run build && open chrome://extensions/", "lint": "eslint . --ext .ts,.tsx", "format": "prettier --write ."}, "devDependencies": {"@preact/preset-vite": "^2.10.2", "@types/chrome": "^0.0.244", "@types/node": "^20.4.2", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "autoprefixer": "^10.4.14", "eslint": "^8.49.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "postcss": "^8.4.24", "preact": "^10.15.1", "prettier": "^3.0.3", "tailwindcss": "^3.3.3", "typescript": "^5.2.2", "vite": "^4.4.9", "vite-plugin-static-copy": "^0.15.0"}, "dependencies": {"preact": "^10.15.1"}}