import { defineConfig } from 'vite';
import preact from '@preact/preset-vite';
import { viteStaticCopy } from 'vite-plugin-static-copy';

export default defineConfig({
  plugins: [
    preact({ devtoolsInProd: true }),
    viteStaticCopy({
      targets: [
        { src: 'manifest.json', dest: '.' },
        { src: 'icons', dest: '.' },
        { src: 'src/content-script/chat-icon.svg', dest: 'content-script' },
      ],
    }),
  ],
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      input: {
        'content-script/index': 'src/content-script/index.tsx',
      },
      output: {
        entryFileNames: '[name].js',
        assetFileNames: 'content-script/[name].[ext]',
      },
    },
  },
});
