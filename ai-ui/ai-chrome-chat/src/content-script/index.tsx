import { render } from 'preact';
import ChatWidget from './chat-widget';
import './chat-icon.svg';
import '../styles/tailwind.css';

function injectChat() {
  // 聊天图标
  const icon = document.createElement('div');
  icon.id = 'ai-chat-icon';
  icon.style.position = 'fixed';
  icon.style.bottom = '32px';
  icon.style.right = '32px';
  icon.style.zIndex = '99999';
  icon.style.cursor = 'pointer';
  icon.innerHTML = `<img src="${chrome.runtime.getURL('content-script/chat-icon.svg')}" width="48" height="48" style="box-shadow:0 2px 8px #0002;border-radius:50%;background:#fff;" />`;
  document.body.appendChild(icon);

  // 聊天窗口容器
  const chatRoot = document.createElement('div');
  chatRoot.id = 'ai-chat-root';
  document.body.appendChild(chatRoot);

  let chatVisible = false;
  const showChat = () => {
    chatRoot.style.display = '';
    chatVisible = true;
  };
  const hideChat = () => {
    chatRoot.style.display = 'none';
    chatVisible = false;
  };
  hideChat();

  icon.addEventListener('click', () => {
    if (chatVisible) {
      hideChat();
    } else {
      showChat();
    }
  });

  render(<ChatWidget />, chatRoot);
}

if (document.readyState === 'loading') {
  window.addEventListener('DOMContentLoaded', injectChat);
} else {
  injectChat();
}
