import { useState, useRef } from 'preact/hooks';
import { chatWithAI, Message } from '../api/chat-api';
import '../styles/tailwind.css';

const DEFAULT_MODEL = 'qwen3:4b';

export default function ChatWidget() {
  const [open, setOpen] = useState(true);
  const [minimized, setMinimized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSend = async () => {
    if (!input.trim()) return;
    const userMsg: Message = { role: 'user', content: input };
    const newMessages: Message[] = [...messages, userMsg];
    setMessages(newMessages);
    setInput('');
    setLoading(true);
    setError(null);

    try {
      const res = await chatWithAI(newMessages, DEFAULT_MODEL);
      const aiMsg = res.choices[0].message;
      const assistantMsg: Message = { ...aiMsg, role: 'assistant' };
      setMessages([...newMessages, assistantMsg]);
    } catch (e) {
      const err = e as { message?: string };
      setError(err.message || 'AI服务异常');
    }
    setLoading(false);
    setTimeout(scrollToBottom, 100);
  };

  return (
    <div className="p-4 bg-purple-50 rounded shadow w-96 border border-purple-200">
      <div className="h-64 overflow-y-auto mb-2 border rounded p-2 bg-white border-purple-100">
        {messages.length === 0 && (
          <div className="text-purple-400 text-center mt-8">和 AI 聊天吧~</div>
        )}
        {messages.map((msg, idx) => (
          <div key={idx} className={msg.role === 'user' ? 'text-right mb-2' : 'text-left mb-2'}>
            <span
              className={
                msg.role === 'user'
                  ? 'inline-block bg-purple-100 text-purple-800 px-3 py-1 rounded-lg'
                  : 'inline-block bg-purple-50 text-purple-700 px-3 py-1 rounded-lg'
              }
            >
              {msg.role === 'user' ? '我' : 'AI'}: {msg.content}
            </span>
          </div>
        ))}
        {loading && <div className="text-purple-400 text-center mt-2">AI 正在思考...</div>}
        <div ref={messagesEndRef} />
      </div>
      {error && <div className="text-red-500 text-center mt-2">{error}</div>}
      <div className="flex items-center border-t px-2 py-2 bg-white border-purple-100">
        <input
          className="flex-1 border border-purple-300 rounded px-2 py-1 mr-2 focus:outline-none focus:border-purple-500"
          type="text"
          value={input}
          placeholder="请输入你的问题..."
          onInput={(e) => setInput((e.target as HTMLInputElement).value)}
          onKeyDown={(e) => {
            if ((e as KeyboardEvent).key === 'Enter') handleSend();
          }}
          disabled={loading}
        />
        <button
          className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-1 rounded disabled:opacity-50"
          onClick={handleSend}
          disabled={loading || !input.trim()}
        >
          发送
        </button>
      </div>
    </div>
  );
}
