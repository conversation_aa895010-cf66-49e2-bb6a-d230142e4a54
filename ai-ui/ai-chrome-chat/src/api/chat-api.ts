// 消息类型定义
export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

// 请求体类型
type ChatRequest = {
  messages: Message[];
  model?: string;
};

// 服务端返回的标准结构（Ollama响应结构，简化版）
export interface ChatResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: Message;
    finish_reason: string;
  }>;
  usage?: unknown;
  [key: string]: unknown;
}

// 错误结构
type ChatError = {
  error: {
    message: string;
    type: string;
    [key: string]: unknown;
  };
};

const API_BASE = 'http://localhost:3000';

// 与服务端交互的API方法
export async function chatWithAI(messages: Message[], model?: string): Promise<ChatResponse> {
  const body: ChatRequest = { messages };
  if (model) body.model = model;

  const resp = await fetch(`${API_BASE}/api/chat`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body),
  });

  const data = await resp.json();

  if (!resp.ok || data.error) {
    // 抛出错误，便于前端捕获
    throw data.error || { message: '网络错误', type: 'network_error' };
  }

  return data as ChatResponse;
}
