import json

def sec_to_srt_time(seconds):
    hrs = int(seconds // 3600)
    mins = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millis = int((seconds - int(seconds)) * 1000)
    return f"{hrs:02}:{mins:02}:{secs:02},{millis:03}"

def json_to_srt(json_file="transcript_with_emotions.json", srt_file="output.srt"):
    with open(json_file, "r", encoding="utf-8") as f:
        data = json.load(f)
    with open(srt_file, "w", encoding="utf-8") as f:
        for idx, entry in enumerate(data, 1):
            start_time = sec_to_srt_time(entry['start'])
            end_time = sec_to_srt_time(entry['end'])
            speaker = entry['speaker']
            emotion = entry['emotion']
            text = entry['text']
            f.write(f"{idx}\n{start_time} --> {end_time}\n{speaker} [{emotion}]: {text}\n\n")
    print(f"[INFO] SRT 字幕文件已生成：{srt_file}")

if __name__ == "__main__":
    json_to_srt()
