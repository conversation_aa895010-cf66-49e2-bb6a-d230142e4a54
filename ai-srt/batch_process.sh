#!/bin/bash
set -e
for video in *.mp4; do
  base="${video%.*}"
  echo "============================="
  echo "[INFO] 处理视频文件：$video"
  echo "============================="
  ffmpeg -y -i "$video" -vn -acodec pcm_s16le -ar 16000 -ac 1 "${base}.wav"
  python main.py "$video"
  python json_to_srt.py
  mv transcript_with_emotions.json "${base}.json"
  mv output.srt "${base}.srt"
  echo "[INFO] 处理完成：$video -> ${base}.json / ${base}.srt"
done
echo "[INFO] 所有文件处理完成！"
