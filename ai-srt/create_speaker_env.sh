#!/bin/bash
set -e

echo "🔹 创建并激活 conda 环境 speaker_env (Python 3.9)..."
conda create -y -n speaker_env python=3.9
source "$(conda info --base)/etc/profile.d/conda.sh"
conda activate speaker_env

echo "🔹 安装 PyTorch 1.10.0 + torchaudio 0.10.0（CPU 版本，兼容 Apple Silicon）..."
conda install -y pytorch=1.10.0 torchaudio=0.10.0 cpuonly -c pytorch -c conda-forge

echo "🔹 安装其他依赖（pyannote.audio 0.x，whisper，transformers）..."
pip install "pyannote.audio<1.0.0" openai-whisper transformers

echo "✅ 环境 speaker_env 已准备完成！"
echo "   使用方法：conda activate speaker_env"