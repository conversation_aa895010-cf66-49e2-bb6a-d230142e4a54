import os

files = {
    "main.py": """
import os
import sys
import json
import torch
from pyannote.audio import Pipeline
import whisper
from transformers import pipeline as hf_pipeline

def extract_audio(video_file, audio_file="audio.wav"):
    os.system(f"ffmpeg -y -i \\"{video_file}\\" -vn -acodec pcm_s16le -ar 16000 -ac 1 \\"{audio_file}\\"")
    return audio_file

def diarize_audio(audio_file):
    print("[INFO] 加载 pyannote 说话人分离模型...")
    pipeline = Pipeline.from_pretrained("pyannote/speaker-diarization")
    diarization = pipeline(audio_file)
    segments = []
    for turn, _, speaker in diarization.itertracks(yield_label=True):
        segments.append({"speaker": speaker, "start": turn.start, "end": turn.end})
    print(f"[INFO] 说话人分离完成，共 {len(segments)} 个片段")
    return segments

def transcribe_and_analyze(audio_file, segments):
    print("[INFO] 加载 Whisper 模型...")
    whisper_model = whisper.load_model("base")
    print("[INFO] 加载情绪分析模型...")
    sentiment_model = hf_pipeline("sentiment-analysis", model="uer/roberta-base-finetuned-jd-binary-chinese")
    results = []
    for idx, seg in enumerate(segments):
        start, end = seg["start"], seg["end"]
        speaker = seg["speaker"]
        segment_file = f"tmp_{speaker}_{idx}.wav"
        os.system(f"ffmpeg -y -i \\"{audio_file}\\" -ss {start} -to {end} -c copy \\"{segment_file}\\"")
        print(f"[INFO] 转录 {speaker} [{start:.2f}s - {end:.2f}s] ...")
        res = whisper_model.transcribe(segment_file)
        text = res['text'].strip()
        print(f"[INFO] 分析情绪: {text}")
        emo_result = sentiment_model(text)
        emotion = emo_result[0]['label']
        results.append({"speaker": speaker, "start": float(f"{start:.2f}"), "end": float(f"{end:.2f}"), "text": text, "emotion": emotion})
        os.remove(segment_file)
    return results

def save_json(results, output_file="transcript_with_emotions.json"):
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"[INFO] 转录完成，结果保存在 {output_file}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print(f"用法: python {sys.argv[0]} 视频文件")
        sys.exit(1)
    video_file = sys.argv[1]
    audio_file = extract_audio(video_file)
    segments = diarize_audio(audio_file)
    results = transcribe_and_analyze(audio_file, segments)
    save_json(results)
""",

    "json_to_srt.py": """
import json

def sec_to_srt_time(seconds):
    hrs = int(seconds // 3600)
    mins = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millis = int((seconds - int(seconds)) * 1000)
    return f"{hrs:02}:{mins:02}:{secs:02},{millis:03}"

def json_to_srt(json_file="transcript_with_emotions.json", srt_file="output.srt"):
    with open(json_file, "r", encoding="utf-8") as f:
        data = json.load(f)
    with open(srt_file, "w", encoding="utf-8") as f:
        for idx, entry in enumerate(data, 1):
            start_time = sec_to_srt_time(entry['start'])
            end_time = sec_to_srt_time(entry['end'])
            speaker = entry['speaker']
            emotion = entry['emotion']
            text = entry['text']
            f.write(f"{idx}\\n{start_time} --> {end_time}\\n{speaker} [{emotion}]: {text}\\n\\n")
    print(f"[INFO] SRT 字幕文件已生成：{srt_file}")

if __name__ == "__main__":
    json_to_srt()
""",

    "batch_process.sh": """
#!/bin/bash
set -e
for video in *.mp4; do
  base="${video%.*}"
  echo "============================="
  echo "[INFO] 处理视频文件：$video"
  echo "============================="
  ffmpeg -y -i "$video" -vn -acodec pcm_s16le -ar 16000 -ac 1 "${base}.wav"
  python main.py "$video"
  python json_to_srt.py
  mv transcript_with_emotions.json "${base}.json"
  mv output.srt "${base}.srt"
  echo "[INFO] 处理完成：$video -> ${base}.json / ${base}.srt"
done
echo "[INFO] 所有文件处理完成！"
""",

    "batch_process.bat": """
@echo off
setlocal enabledelayedexpansion
for %%f in (*.mp4) do (
  set "filename=%%~nf"
  echo =============================
  echo [INFO] 处理视频文件：%%f
  echo =============================
  ffmpeg -y -i "%%f" -vn -acodec pcm_s16le -ar 16000 -ac 1 "!filename!.wav"
  python main.py "%%f"
  python json_to_srt.py
  ren transcript_with_emotions.json "!filename!.json"
  ren output.srt "!filename!.srt"
  echo [INFO] 处理完成：%%f -> !filename!.json / !filename!.srt"
)
echo [INFO] 所有文件处理完成！
pause
""",

    "requirements.txt": """
torch
torchaudio
pyannote.audio
whisper
transformers
""",

    "README.md": """
# Speaker Transcriber

本项目支持对视频文件：
✅ 提取音频
✅ 说话人分离
✅ 语音转录
✅ 情绪分析
✅ 生成带时间轴的 JSON 和 SRT 字幕

## 使用方法

1. 安装依赖：
   pip install -r requirements.txt

2. 单个视频：
   python main.py your_video.mp4
   python json_to_srt.py

3. 批量处理（Linux/Mac）：
   chmod +x batch_process.sh
   ./batch_process.sh

4. 批量处理（Windows）：
   batch_process.bat
"""
}

for fname, content in files.items():
    with open(fname, "w", encoding="utf-8") as f:
        f.write(content.strip() + "\n")
    print(f"[INFO] 已生成文件: {fname}")
