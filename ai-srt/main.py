import os
import sys
import json
import torch
from pyannote.audio import Pipeline
import whisper
from transformers import pipeline as hf_pipeline

def extract_audio(video_file, audio_file="audio.wav"):
    os.system(f"ffmpeg -y -i \"{video_file}\" -vn -acodec pcm_s16le -ar 16000 -ac 1 \"{audio_file}\"")
    return audio_file

def diarize_audio(audio_file):
    print("[INFO] 加载 pyannote 说话人分离模型...")
    pipeline = Pipeline.from_pretrained("pyannote/speaker-diarization")
    diarization = pipeline(audio_file)
    segments = []
    for turn, _, speaker in diarization.itertracks(yield_label=True):
        segments.append({"speaker": speaker, "start": turn.start, "end": turn.end})
    print(f"[INFO] 说话人分离完成，共 {len(segments)} 个片段")
    return segments

# def transcribe_and_analyze(audio_file, segments):
#     print("[INFO] 加载 Whisper 模型...")
#     whisper_model = whisper.load_model("base")
#     print("[INFO] 加载情绪分析模型...")
#     sentiment_model = hf_pipeline("sentiment-analysis", model="uer/roberta-base-finetuned-jd-binary-chinese")
#     results = []
#     for idx, seg in enumerate(segments):
#         start, end = seg["start"], seg["end"]
#         speaker = seg["speaker"]
#         segment_file = f"tmp_{speaker}_{idx}.wav"
#         os.system(f"ffmpeg -y -i \"{audio_file}\" -ss {start} -to {end} -c copy \"{segment_file}\"")
#         print(f"[INFO] 转录 {speaker} [{start:.2f}s - {end:.2f}s] ...")
#         res = whisper_model.transcribe(segment_file)
#         text = res['text'].strip()
#         print(f"[INFO] 分析情绪: {text}")
#         emo_result = sentiment_model(text)
#         emotion = emo_result[0]['label']
#         results.append({"speaker": speaker, "start": float(f"{start:.2f}"), "end": float(f"{end:.2f}"), "text": text, "emotion": emotion})
#         os.remove(segment_file)
#     return results

def transcribe_and_analyze(audio_file, segments):
    print("[INFO] 加载 Whisper 模型...")
    whisper_model = whisper.load_model("base")
    print("[INFO] 加载情绪分析模型...")
    sentiment_model = hf_pipeline("sentiment-analysis", model="uer/roberta-base-finetuned-jd-binary-chinese")
    results = []

    for idx, seg in enumerate(segments):
        start, end = seg["start"], seg["end"]
        speaker = seg["speaker"]
        segment_file = f"tmp_{speaker}_{idx}.wav"
        os.system(f"ffmpeg -y -i \"{audio_file}\" -ss {start} -to {end} -c copy \"{segment_file}\"")

        print(f"[INFO] 转录 {speaker} [{start:.2f}s - {end:.2f}s] ...")
        res = whisper_model.transcribe(segment_file)
        text = res['text'].strip()

        print(f"[INFO] 分析情绪: {text}")
        emo_result = sentiment_model(text)
        emotion_raw = emo_result[0]['label']
        # 去掉括号内的星级描述，只保留标签
        emotion = emotion_raw.split('(')[0].strip()

        results.append({
            "speaker": speaker,
            "start": float(f"{start:.2f}"),
            "end": float(f"{end:.2f}"),
            "text": text,
            "emotion": emotion
        })

        os.remove(segment_file)

    return results

def save_json(results, output_file="transcript_with_emotions.json"):
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"[INFO] 转录完成，结果保存在 {output_file}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print(f"用法: python {sys.argv[0]} 视频文件")
        sys.exit(1)
    video_file = sys.argv[1]
    audio_file = extract_audio(video_file)
    segments = diarize_audio(audio_file)
    results = transcribe_and_analyze(audio_file, segments)
    save_json(results)
