@echo off
setlocal enabledelayedexpansion
for %%f in (*.mp4) do (
  set "filename=%%~nf"
  echo =============================
  echo [INFO] 处理视频文件：%%f
  echo =============================
  ffmpeg -y -i "%%f" -vn -acodec pcm_s16le -ar 16000 -ac 1 "!filename!.wav"
  python main.py "%%f"
  python json_to_srt.py
  ren transcript_with_emotions.json "!filename!.json"
  ren output.srt "!filename!.srt"
  echo [INFO] 处理完成：%%f -> !filename!.json / !filename!.srt"
)
echo [INFO] 所有文件处理完成！
pause
