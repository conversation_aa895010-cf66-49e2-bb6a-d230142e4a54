#!/usr/bin/env python3
from flask import Flask, request, jsonify
import os
import cv2
import imagehash
import json
import subprocess
import numpy as np
from PIL import Image
from skimage.metrics import structural_similarity as ssim
from datetime import datetime

app = Flask(__name__)

def extract_keyframes(input_video, output_log, threshold, ssim_threshold,
                      min_interval, frame_duration, generate_video,
                      start_time=0.0, end_time=None):
    cap = cv2.VideoCapture(input_video)
    if not cap.isOpened():
        raise RuntimeError(f"无法打开视频文件：{input_video}")

    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    start_frame = int(start_time * fps)
    end_frame = int(end_time * fps) if end_time else total_frames

    print(f"[INFO] 视频总时长：{duration:.2f}s，总帧数：{total_frames}, FPS：{fps:.2f}")
    print(f"[INFO] 提取范围：{start_time:.2f}s - {end_time if end_time else duration:.2f}s")

    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)

    output_dir = "keyframes"
    os.makedirs(output_dir, exist_ok=True)

    frame_idx = start_frame
    last_hash, last_gray = None, None
    last_saved_time = None
    results = []

    while cap.isOpened() and frame_idx < end_frame:
        ret, frame = cap.read()
        if not ret:
            break

        timestamp = frame_idx / fps
        pil_img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        curr_hash = imagehash.phash(pil_img)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        record_frame = False
        if last_hash is None:
            record_frame = True
        else:
            hash_diff = last_hash - curr_hash
            ssim_score = None
            if hash_diff > threshold:
                record_frame = True
            elif hash_diff > threshold - 2:
                ssim_score, _ = ssim(last_gray, gray, full=True)
                if ssim_score < ssim_threshold:
                    record_frame = True

            if record_frame and last_saved_time is not None:
                if timestamp - last_saved_time < min_interval:
                    record_frame = False

        if record_frame:
            filename = f"frame_{frame_idx:06d}_{timestamp:.2f}s.jpg"
            filepath = os.path.join(output_dir, filename)
            cv2.imwrite(filepath, frame)
            results.append({
                "frame_idx": frame_idx,
                "timestamp": timestamp,
                "hash": str(curr_hash),
                "file": filename,
            })
            last_hash = curr_hash
            last_gray = gray
            last_saved_time = timestamp

        frame_idx += 1

    cap.release()

    with open(output_log, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    list_file = "list.txt"
    with open(list_file, "w", encoding="utf-8") as f:
        for frame in results:
            f.write(f"file 'keyframes/{frame['file']}'\n")
            f.write(f"duration {frame_duration}\n")
        if results:
            f.write(f"file 'keyframes/{results[-1]['file']}'\n")

    preview_video = None
    if generate_video:
        preview_video = "preview.mp4"
        cmd = [
            "ffmpeg", "-y", "-f", "concat", "-safe", "0",
            "-i", list_file,
            "-vsync", "vfr",
            "-pix_fmt", "yuv420p",
            preview_video
        ]
        subprocess.run(cmd, check=True)

    return {
        "keyframe_count": len(results),
        "output_log": output_log,
        "preview_video": preview_video,
        "first_frame_timestamp": results[0]["timestamp"] if results else None,
        "last_frame_timestamp": results[-1]["timestamp"] if results else None
    }

@app.route("/mcp", methods=["POST"])
def handle_mcp():
    start_time = datetime.utcnow()
    req = request.get_json()
    if not req or req.get("command") != "extract_keyframes":
        return jsonify({
            "command": req.get("command", "unknown"),
            "status": "failure",
            "version": "1.0",
            "error": {
                "message": "Invalid or missing command",
                "code": "E_INVALID_COMMAND"
            },
            "metadata": {
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "server_id": "keyframe-mcp-server-01"
            }
        }), 400

    params = req["params"]
    try:
        result = extract_keyframes(
            input_video=params["input_video"],
            output_log=params["output_log"],
            threshold=params.get("threshold", 5),
            ssim_threshold=params.get("ssim_threshold", 0.98),
            min_interval=params.get("min_interval", 0.0),
            frame_duration=params.get("frame_duration", 0.5),
            generate_video=params.get("generate_video", False),
            start_time=params.get("start_time", 0.0),
            end_time=params.get("end_time", None),
        )
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()

        return jsonify({
            "command": "extract_keyframes",
            "status": "success",
            "version": "1.0",
            "results": result,
            "metadata": {
                "timestamp": end_time.isoformat() + "Z",
                "processing_time_seconds": duration,
                "server_id": "keyframe-mcp-server-01"
            }
        })
    except Exception as e:
        end_time = datetime.utcnow()
        return jsonify({
            "command": "extract_keyframes",
            "status": "failure",
            "version": "1.0",
            "error": {
                "message": str(e),
                "code": "E_PROCESSING"
            },
            "metadata": {
                "timestamp": end_time.isoformat() + "Z",
                "server_id": "keyframe-mcp-server-01"
            }
        }), 500

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8000)