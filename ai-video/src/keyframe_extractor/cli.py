import argparse
import os
import cv2
from .extractor import (
    extract_keyframes,
    save_results_json,
    generate_list_txt,
    create_preview_video
)

def parse_args():
    parser = argparse.ArgumentParser(
        description="关键帧提取并可选生成浏览预览视频（pHash+SSIM+最小间隔）"
    )
    parser.add_argument("input_video", help="输入视频文件路径")
    parser.add_argument("output_log", help="输出 JSON 日志文件路径")
    parser.add_argument("--start-time", type=float, default=0.0, help="开始处理时间（秒）")
    parser.add_argument("--end-time", type=float, default=None, help="结束处理时间（秒）")
    parser.add_argument("--threshold", type=int, default=5, help="pHash 阈值，默认5")
    parser.add_argument("--ssim-threshold", type=float, default=0.98, help="SSIM 相似度阈值，默认0.98")
    parser.add_argument("--frame-duration", type=float, default=0.5, help="预览视频中每帧显示时长（秒），默认0.5")
    parser.add_argument("--min-interval", type=float, default=0.0, help="关键帧最小时间间隔（秒），默认0")
    parser.add_argument("--generate-video", action="store_true", help="是否生成关键帧浏览预览视频，缺省不生成")
    return parser.parse_args()

def main():
    args = parse_args()
    cap = cv2.VideoCapture(args.input_video)
    if not cap.isOpened():
        raise RuntimeError(f"[ERROR] 无法打开视频文件：{args.input_video}")

    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    start_frame = int(args.start_time * fps)
    end_frame = int(args.end_time * fps) if args.end_time else total_frames

    print(f"[INFO] 视频总时长：{duration:.2f}s，总帧数：{total_frames}，FPS：{fps:.2f}")
    print(f"[INFO] 提取范围：{args.start_time:.2f}s - {args.end_time if args.end_time else duration:.2f}s")

    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)

    output_dir = "keyframes"
    os.makedirs(output_dir, exist_ok=True)

    results = extract_keyframes(cap, fps, start_frame, end_frame, args, output_dir)
    save_results_json(results, args.output_log)
    list_file = generate_list_txt(results, args.frame_duration)

    if args.generate_video:
        create_preview_video(list_file)