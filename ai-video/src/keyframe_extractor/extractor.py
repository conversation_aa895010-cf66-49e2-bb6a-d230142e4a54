import cv2
import imagehash
from PIL import Image
import json
import os
import numpy as np
import subprocess
from skimage.metrics import structural_similarity as ssim

def extract_keyframes(cap, fps, start_frame, end_frame, args, output_dir):
    frame_idx = start_frame
    last_hash, last_gray = None, None
    last_saved_time = None
    results = []

    while cap.isOpened() and frame_idx < end_frame:
        ret, frame = cap.read()
        if not ret:
            break

        timestamp = frame_idx / fps
        pil_img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        curr_hash = imagehash.phash(pil_img)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        record_frame = False
        if last_hash is None:
            record_frame = True
        else:
            hash_diff = last_hash - curr_hash
            ssim_score = None
            if hash_diff > args.threshold:
                record_frame = True
            elif hash_diff > args.threshold - 2:
                ssim_score, _ = ssim(last_gray, gray, full=True)
                if ssim_score < args.ssim_threshold:
                    record_frame = True

            if record_frame and last_saved_time is not None:
                if timestamp - last_saved_time < args.min_interval:
                    record_frame = False

        if record_frame:
            print(f"[INFO] 记录关键帧: frame {frame_idx}, time {timestamp:.2f}s")
            filename = f"frame_{frame_idx:06d}_{timestamp:.2f}s.jpg"
            filepath = os.path.join(output_dir, filename)
            cv2.imwrite(filepath, frame)
            results.append({
                "frame_idx": frame_idx,
                "timestamp": timestamp,
                "hash": str(curr_hash),
                "file": filename,
            })
            last_hash = curr_hash
            last_gray = gray
            last_saved_time = timestamp

        frame_idx += 1

    cap.release()
    return results

def save_results_json(results, output_log):
    with open(output_log, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"[INFO] 提取完成，共保存 {len(results)} 张关键帧。日志已保存到 {output_log}")

def generate_list_txt(results, frame_duration):
    list_file = "list.txt"
    with open(list_file, "w", encoding="utf-8") as f:
        for frame in results:
            f.write(f"file 'keyframes/{frame['file']}'\n")
            f.write(f"duration {frame_duration}\n")
        if results:
            f.write(f"file 'keyframes/{results[-1]['file']}'\n")
    print(f"[INFO] 已生成 list.txt，用于 ffmpeg 合成预览视频。")
    return list_file

def create_preview_video(list_file, output_video="preview.mp4"):
    cmd = [
        "ffmpeg", "-y", "-f", "concat", "-safe", "0",
        "-i", list_file,
        "-vsync", "vfr",
        "-pix_fmt", "yuv420p",
        output_video
    ]
    print(f"[INFO] 调用 ffmpeg 生成预览视频：{' '.join(cmd)}")
    subprocess.run(cmd, check=True)
    print(f"[INFO] 预览视频已生成：{output_video}")