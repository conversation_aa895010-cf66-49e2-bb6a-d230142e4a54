import cv2
import imagehash
from PIL import Image
import argparse
import json
import os
import numpy as np
from skimage.metrics import structural_similarity as ssim

def parse_args():
    parser = argparse.ArgumentParser(
        description="从视频中提取关键帧，记录时间戳，并排除相似帧（pHash+SSIM双重判断）。"
    )
    parser.add_argument("input_video", help="输入视频文件路径")
    parser.add_argument("output_log", help="输出 JSON 日志文件路径")
    parser.add_argument("--start-time", type=float, default=0.0, help="开始处理时间（秒）")
    parser.add_argument("--end-time", type=float, default=None, help="结束处理时间（秒）")
    parser.add_argument("--threshold", type=int, default=5, help="pHash 阈值，默认5")
    parser.add_argument("--ssim-threshold", type=float, default=0.98, help="SSIM 相似度阈值，默认0.98")
    return parser.parse_args()

def main():
    args = parse_args()
    cap = cv2.VideoCapture(args.input_video)
    if not cap.isOpened():
        raise RuntimeError(f"[ERROR] 无法打开视频文件：{args.input_video}")

    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    start_frame = int(args.start_time * fps)
    end_frame = int(args.end_time * fps) if args.end_time else total_frames

    print(f"[INFO] 视频总时长：{duration:.2f}s，总帧数：{total_frames}，FPS：{fps:.2f}")
    print(f"[INFO] 提取范围：{args.start_time:.2f}s - {args.end_time if args.end_time else duration:.2f}s")

    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
    frame_idx = start_frame
    last_hash, last_gray = None, None
    results = []

    output_dir = "keyframes"
    os.makedirs(output_dir, exist_ok=True)

    while cap.isOpened() and frame_idx < end_frame:
        ret, frame = cap.read()
        if not ret:
            break

        timestamp = frame_idx / fps
        pil_img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        curr_hash = imagehash.phash(pil_img)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        record_frame = False
        if last_hash is None:
            record_frame = True
        else:
            hash_diff = last_hash - curr_hash
            if hash_diff > args.threshold:
                record_frame = True
            elif hash_diff > args.threshold - 2:  # hash接近阈值时用SSIM做二次确认
                ssim_score, _ = ssim(last_gray, gray, full=True)
                if ssim_score < args.ssim_threshold:
                    record_frame = True

        if record_frame:
            print(f"[INFO] 记录关键帧: frame {frame_idx}, time {timestamp:.2f}s")
            filename = f"frame_{frame_idx:06d}_{timestamp:.2f}s.jpg"
            filepath = os.path.join(output_dir, filename)
            cv2.imwrite(filepath, frame)
            results.append({
                "frame_idx": frame_idx,
                "timestamp": timestamp,
                "hash": str(curr_hash),
                "file": filename,
            })
            last_hash = curr_hash
            last_gray = gray

        frame_idx += 1

    cap.release()

    with open(args.output_log, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"[INFO] 提取完成，共保存 {len(results)} 张关键帧。日志已保存到 {args.output_log}。")

if __name__ == "__main__":
    main()