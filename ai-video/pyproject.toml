[tool.poetry]
name = "keyframe-extractor"
version = "0.1.0"
description = "A tool for extracting keyframes from video with pHash, SSIM, and interval control."
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{ include = "keyframe_extractor", from = "src" }]

[tool.poetry.dependencies]
python = ">=3.9,<4.0"
opencv-python = "*"
Pillow = "*"
imagehash = "*"
scikit-image = "*"
flask = "^3.1.1"

[tool.poetry.scripts]
keyframe-extract = "keyframe_extractor.cli:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
mcp-extract = "keyframe_extractor.client:main"