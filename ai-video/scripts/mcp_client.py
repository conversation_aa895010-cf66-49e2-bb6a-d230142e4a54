#!/usr/bin/env python3
import argparse
import requests
import json

def parse_args():
    parser = argparse.ArgumentParser(
        description="MCP 客户端：发送关键帧提取请求到 MCP 服务"
    )
    parser.add_argument("--server-url", type=str, default="http://localhost:8000/mcp", help="MCP 服务URL")
    parser.add_argument("--input-video", type=str, required=True, help="输入视频文件路径")
    parser.add_argument("--output-log", type=str, required=True, help="输出 JSON 日志文件路径")
    parser.add_argument("--threshold", type=int, default=5, help="pHash 阈值")
    parser.add_argument("--ssim-threshold", type=float, default=0.98, help="SSIM 阈值")
    parser.add_argument("--min-interval", type=float, default=0.0, help="关键帧最小时间间隔（秒）")
    parser.add_argument("--frame-duration", type=float, default=0.5, help="预览视频每帧显示时长（秒）")
    parser.add_argument("--generate-video", action="store_true", help="是否生成浏览预览视频")
    parser.add_argument("--start-time", type=float, default=0.0, help="开始处理时间（秒）")
    parser.add_argument("--end-time", type=float, default=None, help="结束处理时间（秒）")
    return parser.parse_args()

def main():
    args = parse_args()

    mcp_request = {
    "command": "extract_keyframes",
    "params": {
        "input_video": args.input_video,
        "output_log": args.output_log,
        "threshold": args.threshold,
        "ssim_threshold": args.ssim_threshold,
        "min_interval": args.min_interval,
        "frame_duration": args.frame_duration,
        "generate_video": args.generate_video,
        "start_time": args.start_time,
        "end_time": args.end_time,
    }
}

    print(f"[INFO] 发送 MCP 请求到 {args.server_url}")
    response = requests.post(
        args.server_url,
        json=mcp_request,
        headers={
    "Content-Type": "application/json"
}
    )

    if response.status_code == 200:
        mcp_response = response.json()
        if mcp_response.get("status") == "success":
            print("[SUCCESS] 关键帧提取完成：")
            print(json.dumps(mcp_response, ensure_ascii=False, indent=2))
        else:
            print(f"[ERROR] MCP 请求失败: {mcp_response.get('error')}")
    else:
        print(f"[ERROR] HTTP 错误: 状态码 {response.status_code}")

if __name__ == "__main__":
    main()