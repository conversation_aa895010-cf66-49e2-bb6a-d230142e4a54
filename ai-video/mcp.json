{"name": "extract_keyframes", "description": "从视频中提取关键帧并生成可选的浏览视频", "parameters": {"type": "object", "properties": {"input_video": {"type": "string", "description": "输入视频文件路径"}, "output_log": {"type": "string", "description": "输出关键帧 JSON 日志文件路径"}, "threshold": {"type": "integer", "description": "pHash 差值阈值，越高保留帧越少，默认5"}, "ssim_threshold": {"type": "number", "description": "SSIM 相似度阈值，默认0.98"}, "min_interval": {"type": "number", "description": "关键帧最小时间间隔（秒），默认0表示不限制"}, "frame_duration": {"type": "number", "description": "合成浏览视频中每帧显示时长（秒），默认0.5"}, "generate_video": {"type": "boolean", "description": "是否生成关键帧浏览预览视频，默认false"}, "start_time": {"type": "number", "description": "开始处理时间（秒），默认0.0"}, "end_time": {"type": ["number", "null"], "description": "结束处理时间（秒），默认null表示到结尾"}}, "required": ["input_video", "output_log"]}}