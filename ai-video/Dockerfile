FROM python:3.11-slim

# 安装必要工具
RUN apt-get update && apt-get install -y curl ffmpeg && rm -rf /var/lib/apt/lists/*

# 安装 Poetry
RUN curl -sSL https://install.python-poetry.org | python3 - && \
    ln -s /root/.local/bin/poetry /usr/local/bin/poetry

# 复制项目文件
WORKDIR /app
COPY . .

# 使用 Poetry 安装依赖
RUN poetry install --no-root

# 将 Poetry 虚拟环境路径加入 PATH
ENV PATH="/app/.venv/bin:$PATH"

# 容器启动时默认执行 MCP 服务
CMD ["python", "services/mcp_service.py"]