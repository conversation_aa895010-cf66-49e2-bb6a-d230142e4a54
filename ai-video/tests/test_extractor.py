import json
import tempfile
import numpy as np
import cv2
import os
import pytest
from keyframe_extractor import extractor

def test_save_results_json(tmp_path):
    # 模拟关键帧结果
    results = [
        {"frame_idx": 0, "timestamp": 0.0, "hash": "abc", "file": "frame_000000.jpg"},
        {"frame_idx": 10, "timestamp": 0.33, "hash": "def", "file": "frame_000010.jpg"},
    ]
    output_file = tmp_path / "test_results.json"
    extractor.save_results_json(results, str(output_file))

    with open(output_file, "r", encoding="utf-8") as f:
        data = json.load(f)
    assert data == results

def test_generate_list_txt(tmp_path):
    results = [
        {"file": "frame_000000.jpg"},
        {"file": "frame_000010.jpg"},
    ]
    os.chdir(tmp_path)
    list_file = extractor.generate_list_txt(results, 0.5)
    assert os.path.exists(list_file)
    with open(list_file, "r", encoding="utf-8") as f:
        content = f.read()
    assert "file" in content
    assert "duration" in content

def test_extract_keyframes_simulated(tmp_path):
    # 用 OpenCV 创建一个非常小的视频文件
    video_path = tmp_path / "test.mp4"
    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
    out = cv2.VideoWriter(str(video_path), fourcc, 1, (64, 64))
    for i in range(5):
        frame = np.full((64, 64, 3), i * 50, dtype=np.uint8)
        out.write(frame)
    out.release()

    cap = cv2.VideoCapture(str(video_path))
    assert cap.isOpened()

    class DummyArgs:
        threshold = 5
        ssim_threshold = 0.98
        min_interval = 0.0

    results = extractor.extract_keyframes(cap, fps=1, start_frame=0, end_frame=5, args=DummyArgs(), output_dir=str(tmp_path))
    assert isinstance(results, list)
    assert all("frame_idx" in r for r in results)