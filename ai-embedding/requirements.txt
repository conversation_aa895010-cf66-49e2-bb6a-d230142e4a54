# requirements.txt
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.11.4
pydantic-settings==2.0.3
python-dotenv==1.0.0
redis==5.0.1
llama-index==0.12.35
llama-index-llms-ollama==0.5.4
llama-index-embeddings-ollama==0.6.0
llama-index-vector-stores-redis==0.5.0
llama-index-vector-stores-chroma==0.4.1
python-multipart==0.0.6
tenacity==8.2.3
sqlalchemy==2.0.23
pymysql==1.1.0
cryptography==41.0.5
tqdm==4.66.1
langchain
ollama
requests
typing
openai
langchain-community
langchain-ollama>=0.0.1
langgraph>=0.0.10
streamlit
python-dotenv