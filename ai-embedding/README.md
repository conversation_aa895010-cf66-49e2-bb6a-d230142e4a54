# Ollama 多功能聊天机器人

## 项目简介
这是一个基于 Ollama 的多功能聊天机器人模块，提供以下核心功能：
- 模型管理
- 代码生成
- 文本翻译
- 文本摘要
- 头脑风暴

## 环境准备
1. 安装 Ollama
```bash
brew install ollama  # macOS
```

2. 拉取模型
```bash
ollama pull llama2
ollama pull mistral
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

## 使用示例
```python
from chat_bot_module import ChatBotModule

bot = ChatBotModule()

# 列出可用模型
models = bot.handle_request("list_models", {})

# 代码生成
code_result = bot.handle_request("code_generation", {
    "language": "Python", 
    "task_description": "创建文件下载器"
})

# 文本翻译
translation_result = bot.handle_request("translation", {
    "text": "Hello, world!", 
    "source_lang": "English", 
    "target_lang": "Chinese"
})
```

## 功能列表
- `list_models`: 列出可用模型
- `pull_model`: 拉取新模型
- `generate`: 文本生成
- `code_generation`: 代码生成
- `translation`: 文本翻译
- `summarization`: 文本摘要
- `brainstorm`: 头脑风暴

## 注意事项
- 需要提前安装 Ollama
- 根据需要选择合适的模型
- 部分功能依赖网络和模型性能 