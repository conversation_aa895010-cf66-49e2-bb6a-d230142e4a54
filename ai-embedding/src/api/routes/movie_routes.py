from fastapi import APIRouter, Depends, HTTPException
from typing import List
from models.schemas import Movie, SearchQuery, SearchResponse
from services.movie_service import MovieService

router = APIRouter(prefix="/api/v1/movies", tags=["movies"])

@router.post("/", response_model=Movie)
async def create_movie(
    movie: Movie,
    service: MovieService = Depends(lambda: MovieService())
):
    try:
        return await service.add_movie(movie)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/search", response_model=SearchResponse)
async def search_movies(
    query: SearchQuery,
    service: MovieService = Depends(lambda: MovieService())
):
    try:
        results = await service.search_movies(query.query, query.max_results)
        return SearchResponse(results=results, total=len(results))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 