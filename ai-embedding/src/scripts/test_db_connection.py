import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from sqlalchemy import text
from models import Movie, get_db
# 加载环境变量
load_dotenv()

def test_connection():
    """测试数据库连接"""
    try:
        # 测试基本连接
        with engine.connect() as conn:
            print("数据库连接成功！")
            
            # 测试查询
            result = conn.execute(text("SELECT COUNT(*) FROM epg_content_movie"))
            count = result.scalar()
            print(f"电影表中共有 {count} 条记录")
            
            # 测试一条记录
            result = conn.execute(text("SELECT * FROM epg_content_movie LIMIT 1"))
            row = result.fetchone()
            if row:
                print("\n示例记录:")
                print(f"标题: {row.title}")
                print(f"导演: {row.director}")
                print(f"演员: {row.actors}")
                print(f"地区: {row.region}")
                print(f"标签: {row.tags}")
                print(f"亮点: {row.highlight}")
            
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")

if __name__ == "__main__":
    test_connection() 