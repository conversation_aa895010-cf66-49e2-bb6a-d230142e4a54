import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any
import json
from dotenv import load_dotenv
from tqdm import tqdm





from models.schemas import Movie
from services.movie_service import MovieService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('movie_processor.log')
    ]
)
logger = logging.getLogger(__name__)

class BaseMovieProcessor:
    """电影处理器基类"""
    
    def __init__(self):
        """初始化处理器"""
        # 加载环境变量
        load_dotenv()
        
        # 初始化服务
        self.movie_service = MovieService()
        logger.info(f"{self.__class__.__name__} 初始化完成")
        
    def load_movies_from_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        从文件加载电影数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            电影数据列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"从文件加载电影数据失败: {str(e)}")
            return []
            
    async def process_movies(self, movies_data: List[Dict[str, Any]]) -> List[Movie]:
        """
        处理电影数据
        
        Args:
            movies_data: 电影数据列表
            
        Returns:
            处理后的电影对象列表
        """
        processed_movies = []
        for data in tqdm(movies_data, desc="处理电影数据"):
            try:
                # 转换为 Movie 对象
                movie = Movie(**data)
                processed_movies.append(movie)
            except Exception as e:
                logger.error(f"处理电影数据失败: {str(e)}")
                continue
        return processed_movies
        
    async def batch_create_movies(self, movies: List[Movie]) -> List[Movie]:
        """
        批量创建电影
        
        Args:
            movies: 电影对象列表
            
        Returns:
            创建成功的电影对象列表
        """
        return await self.movie_service.batch_create_movies(movies) 