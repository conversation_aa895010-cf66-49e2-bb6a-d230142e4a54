import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any
import asyncio
from dotenv import load_dotenv
from tqdm import tqdm

# 添加项目根目录到 Python 路径



# 添加 src 目录到 Python 路径

# 加载环境变量
load_dotenv()

from models.schemas import Movie
from services.movie_service import MovieService
from models.database import MovieDB, get_db
from .base_processor import BaseMovieProcessor
from config.search import DEFAULT_TOP_K, TEST_QUERIES

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('index_creation.log')
    ]
)
logger = logging.getLogger(__name__)

class IndexManager(BaseMovieProcessor):
    """索引管理器"""
    
    def __init__(self):
        """初始化索引管理器"""
        super().__init__()
        
    async def load_movies_from_db(self) -> List[Movie]:
        """
        从数据库加载电影数据
        
        Returns:
            电影对象列表
        """
        movies = []
        db = next(get_db())
        try:
            # 查询所有电影数据
            logger.info("开始从数据库加载电影数据...")
            db_movies = db.query(MovieDB).all()
            total_movies = len(db_movies)
            logger.info(f"从数据库加载了 {total_movies} 条电影数据")
            
            # 转换为 Movie 对象
            logger.info("开始转换电影数据格式...")
            success_count = 0
            error_count = 0
            
            for movie in tqdm(db_movies, desc="转换电影数据"):
                try:
                    movie_obj = Movie(**movie.to_dict())
                    movies.append(movie_obj)
                    success_count += 1
                except Exception as e:
                    logger.error(f"转换电影数据失败: {movie.title if hasattr(movie, 'title') else 'Unknown'} - {str(e)}")
                    error_count += 1
                
            logger.info(f"数据转换完成: 成功 {success_count} 条, 失败 {error_count} 条")
                
        except Exception as e:
            logger.error(f"从数据库加载数据失败: {str(e)}")
        finally:
            db.close()
            
        return movies
    
    async def create_index(self) -> int:
        """
        创建索引
        
        Returns:
            成功创建的记录数
        """
        try:
            # 1. 从数据库加载数据
            logger.info("开始创建索引流程...")
            movies = await self.load_movies_from_db()
            
            if not movies:
                logger.warning("没有找到电影数据，索引创建终止")
                return 0
            
            # 2. 批量添加数据
            logger.info(f"开始创建向量索引，共 {len(movies)} 条数据...")
            results = await self.movie_service.batch_add_movies(movies)
            success_count = sum(results)
            logger.info(f"索引创建完成: 成功 {success_count}/{len(movies)} 条")
            
            return success_count
            
        except Exception as e:
            logger.error(f"创建索引失败: {str(e)}")
            return 0
    
    async def search_test(self, query: str, top_k: int = DEFAULT_TOP_K) -> None:
        """
        测试搜索功能
        
        Args:
            query: 搜索查询
            top_k: 返回结果数量
        """
        try:
            logger.info(f"开始测试搜索: {query}")
            results = await self.movie_service.search_movies(query, top_k)
            logger.info(f"搜索完成，找到 {len(results)} 条结果")
            
            for i, movie in enumerate(results, 1):
                logger.info(f"\n结果 {i}:")
                logger.info(f"标题: {movie.title}")
                logger.info(f"导演: {movie.director}")
                logger.info(f"演员: {movie.actors}")
                logger.info(f"地区: {movie.region}")
                logger.info(f"标签: {movie.tags}")
                logger.info(f"亮点: {movie.highlight}")
                
        except Exception as e:
            logger.error(f"搜索测试失败: {str(e)}")

async def main():
    """主函数"""
    # 创建索引管理器
    logger.info("启动索引创建程序...")
    manager = IndexManager()
    
    # 创建索引
    count = await manager.create_index()
    if count > 0:
        logger.info("\n索引创建成功！")
        
        # 测试搜索
        for query in TEST_QUERIES:
            await manager.search_test(query)
    else:
        logger.error("\n索引创建失败！")

if __name__ == "__main__":
    logger.info("程序开始执行...")
    asyncio.run(main())
    logger.info("程序执行完成") 