import os
import sys
import logging
from pathlib import Path
from typing import List, Optional
import asyncio
from dotenv import load_dotenv

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 添加 src 目录到 Python 路径
src_path = project_root / "src"
sys.path.append(str(src_path))

from models.schemas import Movie, SearchQuery, SearchResponse
from services.movie_service import MovieService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('interactive_search.log')
    ]
)
logger = logging.getLogger(__name__)

class InteractiveSearch:
    def __init__(self):
        """初始化交互式搜索"""
        # 加载环境变量
        load_dotenv()
        
        # 初始化服务
        self.movie_service = MovieService()
        logger.info("InteractiveSearch 初始化完成")
        
    async def search_movies(self, query: str, max_results: int = 10) -> List[Movie]:
        """搜索电影"""
        try:
            search_query = SearchQuery(text=query, max_results=max_results)
            response = await self.movie_service.search_movies(search_query)
            return response.results
        except Exception as e:
            logger.error(f"搜索电影失败: {str(e)}")
            return []
            
    async def get_movie_details(self, content_code: str) -> Optional[Movie]:
        """获取电影详情"""
        try:
            return await self.movie_service.get_movie(content_code)
        except Exception as e:
            logger.error(f"获取电影详情失败: {str(e)}")
            return None
            
    def display_movie(self, movie: Movie):
        """显示电影信息"""
        print("\n电影详情:")
        print(f"标题: {movie.title}")
        print(f"地区: {movie.region}")
        print(f"导演: {movie.director}")
        print(f"主演: {movie.actors}")
        print(f"类型: {movie.genre}")
        print(f"简介: {movie.description}")
        print("-" * 50)

async def main():
    """主函数"""
    search = InteractiveSearch()
    
    while True:
        # 获取搜索查询
        query = input("\n请输入搜索关键词 (输入 'q' 退出): ")
        if query.lower() == 'q':
            break
            
        # 搜索电影
        movies = await search.search_movies(query)
        
        # 显示搜索结果
        if movies:
            print(f"\n找到 {len(movies)} 个结果:")
            for i, movie in enumerate(movies, 1):
                print(f"{i}. {movie.title} ({movie.region})")
                
            # 获取详细信息
            while True:
                choice = input("\n请输入编号查看详情 (输入 'b' 返回搜索): ")
                if choice.lower() == 'b':
                    break
                    
                try:
                    index = int(choice) - 1
                    if 0 <= index < len(movies):
                        movie = await search.get_movie_details(movies[index].content_code)
                        if movie:
                            search.display_movie(movie)
                    else:
                        print("无效的编号")
                except ValueError:
                    print("请输入有效的编号")
        else:
            print("未找到相关电影")

if __name__ == "__main__":
    asyncio.run(main()) 