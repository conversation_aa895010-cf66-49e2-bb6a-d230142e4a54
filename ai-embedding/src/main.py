from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware


app = FastAPI()

from services.agent_service import AgentService

class ChatRequest(BaseModel):
    query: str




@app.post("/chat")
async def chat_endpoint(req: ChatRequest):
    try:
        response = agent.run(req.query)
        if not response or "我无法" in response or "不知道" in response:
            return {"response": fallback_tool(req.query)}
        return {"response": response}
    except Exception as e:
        return {"response": f"❗️出错了：{str(e)}"}
# 注册路由
if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--cli", action="store_true", help="是否使用 CLI 模式")
    args = parser.parse_args()

    if args.cli:
        while True:
            user_input = input("👤 你: ")
            if user_input.lower() in ["exit", "quit"]:
                break
            try:
                response = agent.run(user_input)
                if not response or "我无法" in response or "不知道" in response:
                    print(f"🤖 {fallback_tool(user_input)}\n")
                else:
                    print(f"🤖 {response}\n")
            except Exception as e:
                print(f"❗️ 出错了：{e}\n")
    else:
        uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
