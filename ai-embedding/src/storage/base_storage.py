from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
import logging
from config.weights import MOVIE_FIELD_WEIGHTS
from utils.text_processor import TextProcessor

logger = logging.getLogger(__name__)

class BaseMovieStorage(ABC):
    """电影存储基类"""
    
    def __init__(self):
        """初始化存储"""
        self.text_processor = TextProcessor()
        
    def _format_movie_text(self, movie_data: Dict) -> str:
        """格式化电影文本，根据字段权重"""
        text_parts = []
        
        # 根据权重重复添加字段内容
        for field, weight in MOVIE_FIELD_WEIGHTS.items():
            if field in movie_data and movie_data[field]:
                # 根据权重重复添加字段内容
                text_parts.extend([str(movie_data[field])] * weight)
                
        # 使用空格连接所有文本部分
        return " ".join(text_parts)
    
    @abstractmethod
    async def clear_index(self) -> bool:
        """清除索引"""
        pass
        
    @abstractmethod
    async def get_index_stats(self) -> Optional[Dict]:
        """获取索引统计信息"""
        pass
        
    @abstractmethod
    async def add_movie(self, movie_data: Dict) -> bool:
        """添加电影数据到存储"""
        pass
        
    @abstractmethod
    async def search_movies(self, query: str, top_k: int = 5) -> List[Dict]:
        """搜索电影"""
        pass
        
    @abstractmethod
    async def get_movie_by_id(self, content_code: str) -> Optional[Dict]:
        """根据ID获取电影数据"""
        pass
        
    @abstractmethod
    async def get_all_movies(self) -> List[Dict]:
        """获取所有电影数据"""
        pass 