from llama_index.embeddings.ollama import OllamaEmbedding
from llama_index.core.schema import Document
from llama_index.llms.ollama import Ollama
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.retrievers import VectorIndexRetriever

import chromadb
import requests

from config.base import Settings as AppSettings
from config.weights import MOVIE_FIELD_WEIGHTS
from pathlib import Path
import logging

from typing import List, Dict, Union, Optional, Any
import os
from chromadb.config import Settings as ChromaSettings

from ollama import Client, AsyncClient

from models.schemas import Movie
from utils.text_processor import TextProcessor
from .base_storage import BaseMovieStorage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('chroma_storage.log')
    ]
)
logger = logging.getLogger(__name__)

class MovieChromaStorage(BaseMovieStorage):
    def __init__(
        self, 
        persist_dir: Optional[Union[str, Path]] = None, 
        collection_name: str = "movie_collection"
    ):
        """
        初始化 ChromaDB 存储
        
        Args:
            persist_dir: 持久化存储目录，默认在项目根目录的 'chroma_db' 文件夹
            collection_name: 集合名称
        """
        super().__init__()
        self.settings = AppSettings()

        # 设置默认持久化目录
        if persist_dir is None:
            # 获取项目根目录
            project_root = Path(__file__).parent.parent.parent
            persist_dir = project_root / self.settings.CHROMA_PERSIST_DIR        
        # 确保目录存在
        persist_dir = Path(persist_dir)
        persist_dir.mkdir(parents=True, exist_ok=True)
        
        # 日志记录初始化信息
        logger.info(f"初始化 ChromaDB 存储")
        logger.info(f"持久化目录: {persist_dir}")
        logger.info(f"集合名称: {collection_name}")
        
        try:
            # 初始化自定义嵌入模型
            self.embedding_model = OllamaEmbedding(
                model_name=self.settings.OLLAMA_MODEL,
                base_url=self.settings.OLLAMA_BASE_URL,
                timeout=self.settings.OLLAMA_TIMEOUT
            )
            
            # 初始化 Ollama LLM
            self.llm = Ollama(
                model="gemma3:4b",  # 使用 llama2-chinese 模型
                base_url=self.settings.OLLAMA_BASE_URL,
                request_timeout=self.settings.OLLAMA_TIMEOUT,
                temperature=0.1,  # 降低随机性
                context_window=4096,  # 设置上下文窗口大小
                additional_kwargs={
                    "num_predict": 128,  # 生成的最大token数
                    "stop": ["</s>", "Human:", "Assistant:"]  # 停止词
                }
            )
            
            # 配置 ChromaDB 客户端
            self.chroma_client = chromadb.PersistentClient(
                path=str(persist_dir),
                settings=ChromaSettings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # 尝试导入必要的类
            from llama_index.vector_stores.chroma import ChromaVectorStore
            from llama_index.core import VectorStoreIndex, Settings, StorageContext
            Settings.embed_model = self.embedding_model
            Settings.llm = self.llm
            Settings.chunk_size = 1024

            # 创建或获取集合
            self.collection = self.chroma_client.get_or_create_collection(
                name=collection_name,
                metadata={"hnsw:space": "cosine"},
                embedding_function=None,
            )

            # 创建向量存储 - 使用自定义方法
            self.vector_store = ChromaVectorStore(chroma_collection=self.collection)
            self.storage_context = StorageContext.from_defaults(vector_store=self.vector_store)
            
            # 初始化索引的替代方法
            self.index = VectorStoreIndex.from_vector_store(
                vector_store=self.vector_store,
                embed_model=self.embedding_model,
            )
            
            logger.info("ChromaDB 存储初始化成功")
        
        except Exception as e:
            logger.error(f"ChromaDB 存储初始化失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise
        
        # 创建存储上下文
        self.storage_context = StorageContext.from_defaults(
            vector_store=self.vector_store
        )

        # 初始化向量索引
        self.index = VectorStoreIndex.from_vector_store(
            vector_store=self.vector_store,
            storage_context=self.storage_context,
            show_progress=True
        )
        
        # 初始化检索器
        self.retriever = VectorIndexRetriever(
            index=self.index,
            similarity_top_k=5
        )
        
        # 初始化查询引擎
        self.query_engine = RetrieverQueryEngine.from_args(
            retriever=self.retriever,
            llm=self.llm,
            response_mode="no_text"  # 只返回检索结果，不生成回答
        )

    async def clear_index(self):
        """清除索引"""
        try:
            self.collection.delete()
            return True
        except Exception as e:
            logger.error(f"清除索引失败: {str(e)}")
            return False
    
    async def get_index_stats(self):
        """获取索引统计信息"""
        try:
            count = self.collection.count()
            return {
                "total_movies": count,
                "index_name": self.settings.VECTOR_STORE_INDEX_NAME
            }
        except Exception as e:
            logger.error(f"获取索引统计失败: {str(e)}")
            return None

    async def add_movie(self, movie_data: Dict):
        """添加单个电影数据到向量存储"""
        try:
            # 格式化电影文本
            text = self._format_movie_text(movie_data)
            
            # 创建文档
            document = Document(
                text=text,
                metadata=movie_data
            )
            
            # 添加到向量存储
            self.index.insert(document)
            return True
        except Exception as e:
            logger.error(f"添加电影数据失败: {e}")
            return False
            
    async def search_movies_vector(self, query: str, top_k: int = 5):
        """使用纯向量搜索"""
        try:
            # 获取查询的嵌入向量
            query_embedding = self.embedding_model.get_text_embedding(query)
            
            # 使用 Chroma 的相似度搜索
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=["metadatas", "distances"]  # 包含距离信息
            )
            
            # 处理结果
            movies = []
            if results and results['metadatas']:
                for metadata, distance in zip(results['metadatas'][0], results['distances'][0]):
                    # 将距离转换为相似度分数（0-100）
                    similarity = (1 - distance) * 100
                    metadata['similarity'] = round(similarity, 2)
                    movies.append(metadata)
                    
            return movies
        except Exception as e:
            logger.error(f"向量搜索失败: {str(e)}")
            return []

    async def search_movies_llm(self, query: str, top_k: int = 5):
        """使用 LLM 增强的搜索"""
        try:
            # 使用查询引擎
            response = self.query_engine.query(query)
            
            # 处理结果
            results = []
            for node in response.source_nodes:
                metadata = node.metadata.copy()
                # 使用节点的相似度分数
                similarity = node.score * 100 if hasattr(node, 'score') else 0
                metadata['similarity'] = round(similarity, 2)
                results.append(metadata)
                    
            return results
        except Exception as e:
            logger.error(f"LLM搜索失败: {str(e)}")
            return []

    async def search_movies(self, query: str, top_k: int = 5, use_llm: bool = False):
        """搜索电影，可以选择是否使用 LLM"""
        if use_llm:
            return await self.search_movies_llm(query, top_k)
        else:
            return await self.search_movies_vector(query, top_k)

    async def compare_search_methods(self, query: str, top_k: int = 5):
        """比较两种搜索方法的结果"""
        try:
            # 执行两种搜索
            vector_results = await self.search_movies_vector(query, top_k)
            llm_results = await self.search_movies_llm(query, top_k)
            
            # 比较结果
            comparison = {
                "query": query,
                "vector_search": {
                    "count": len(vector_results),
                    "results": vector_results
                },
                "llm_search": {
                    "count": len(llm_results),
                    "results": llm_results
                }
            }
            
            return comparison
        except Exception as e:
            logger.error(f"比较搜索方法失败: {str(e)}")
            return None

    async def get_movie_by_id(self, content_code: str):
        """根据ID获取电影数据"""
        try:
            results = self.collection.get(
                where={"content_code": content_code}
            )
            if results and results['metadatas']:
                return results['metadatas'][0]
            return None
        except Exception as e:
            logger.error(f"获取电影数据失败: {str(e)}")
            return None
            
    def _format_movie_text(self, movie_data: Dict) -> str:
        """格式化电影文本，根据字段权重"""
        text_parts = []
        
        # 根据权重重复添加字段内容
        for field, weight in MOVIE_FIELD_WEIGHTS.items():
            if field in movie_data and movie_data[field]:
                # 根据权重重复添加字段内容
                text_parts.extend([str(movie_data[field])] * weight)
                
        # 使用空格连接所有文本部分
        return " ".join(text_parts)

    async def get_all_movies(self):
        """获取所有电影数据"""
        try:
            # 从 Chroma 获取所有数据
            results = self.collection.get()
            if not results or not results['metadatas']:
                logger.warning("向量存储中没有电影数据")
                return []
                
            return results['metadatas']
            
        except Exception as e:
            logger.error(f"获取所有电影数据失败: {str(e)}")
            return []

    def batch_add_movies(self, movies: List[Dict], batch_size: int = 100) -> List[bool]:
        results = []
        total = len(movies)

        for i in range(0, total, batch_size):
            try:
                batch = movies[i:i + batch_size]
                logger.info(f"正在处理第 {i + 1} 到 {min(i + batch_size, total)} 条数据...")
                texts = [self._format_movie_text(m) for m in batch]
                response = requests.post(
                    url=f"{self.settings.OLLAMA_BASE_URL}/api/embed",
                    json={"model": self.settings.OLLAMA_MODEL, "input": texts}
                )
                response.raise_for_status()
                data = response.json()
                embeddings = data.get("embeddings")
                if not embeddings:
                    logger.warning("未返回 embedding，响应内容: %s", data)
                    results.extend([False] * len(batch))
                    continue
            
                documents = [
                    Document(text=text, metadata=m, embedding=embedding)
                    for text, m, embedding in zip(texts, batch, embeddings)
                ]
                self.vector_store.add(documents)
                results.extend([True] * len(batch))
                logger.info(f"✅ 成功处理批次 {i // batch_size + 1}")

            except Exception as e:
                logger.error(f"❌ 处理批次 {i // batch_size + 1} 失败: {e}",)
                results.extend([False] * len(batch))

        logger.info(f"✅ 批量添加完成，总共 {total} 条，成功 {results.count(True)} 条，失败 {results.count(False)} 条")
        return results

    async def delete_movie(self, content_code: str) -> bool:
        """删除电影数据
        
        Args:
            content_code: 电影ID
            
        Returns:
            是否删除成功
        """
        try:
            # 使用 where 条件删除指定文档
            self.collection.delete(
                where={"content_code": content_code}
            )
            logger.info(f"成功删除电影: {content_code}")
            return True
        except Exception as e:
            logger.error(f"删除电影失败: {str(e)}")
            return False