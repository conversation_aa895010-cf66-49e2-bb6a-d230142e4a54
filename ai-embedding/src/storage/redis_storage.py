import redis
from llama_index.core import (
    VectorStoreIndex,
    Document,
    StorageContext,
    load_index_from_storage,
    Settings
)
from llama_index.vector_stores.redis import RedisVectorStore
from llama_index.vector_stores.redis.schema import RedisIndexSchema
from llama_index.embeddings.ollama import OllamaEmbedding
from core.config.base import Settings as AppSettings
from core.config.weights import MOVIE_FIELD_WEIGHTS
from typing import Optional, Dict, Any, List
import logging
import json
import os
from pathlib import Path
import asyncio
from dotenv import load_dotenv
from .base_storage import BaseMovieStorage

from models.schemas import Movie

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('redis_storage.log')
    ]
)
logger = logging.getLogger(__name__)

class MovieRedisStorage(BaseMovieStorage):
    def __init__(self):
        """初始化 Redis 存储"""
        super().__init__()
        self.settings = AppSettings()
        
        # 初始化Redis客户端
        self.redis_client = redis.Redis(
            host=self.settings.REDIS_HOST,
            port=int(self.settings.REDIS_PORT),
            db=int(self.settings.REDIS_DB),
            password=self.settings.REDIS_PASSWORD or None,
            decode_responses=True
        )
        
        # 初始化嵌入模型
        self.embedding_model = OllamaEmbedding(
            model_name=self.settings.OLLAMA_MODEL,
            base_url=self.settings.OLLAMA_BASE_URL
        )
        
        # 设置全局设置
        Settings.embed_model = self.embedding_model
        
        # 初始化向量存储
        redis_url = f"redis://{self.settings.REDIS_HOST}:{self.settings.REDIS_PORT}/{self.settings.REDIS_DB}"
        if self.settings.REDIS_PASSWORD:
            redis_url = f"redis://:{self.settings.REDIS_PASSWORD}@{self.settings.REDIS_HOST}:{self.settings.REDIS_PORT}/{self.settings.REDIS_DB}"
        
        # 创建索引模式
        index_schema = RedisIndexSchema(
            prefix=self.settings.VECTOR_STORE_INDEX_PREFIX,
            index_name=self.settings.VECTOR_STORE_INDEX_NAME,
            vector_dim=int(self.settings.VECTOR_DIM),
            vector_distance_metric="COSINE"
        )
            
        self.vector_store = RedisVectorStore(
            redis_url=redis_url,
            index_schema=index_schema,
            key_prefix="movie:",
            text_key="text",
            metadata_key="metadata"
        )
        
        # 创建存储上下文
        self.storage_context = StorageContext.from_defaults(
            vector_store=self.vector_store
        )
        
        # 初始化向量索引
        self.index = VectorStoreIndex.from_vector_store(
            vector_store=self.vector_store,
            storage_context=self.storage_context,
            show_progress=True
        )
    
    async def clear_index(self):
        """清除索引"""
        try:
            # 删除所有相关的键
            keys = self.redis_client.keys("movie:*")
            if keys:
                self.redis_client.delete(*keys)
            return True
        except Exception as e:
            logger.error(f"清除索引失败: {str(e)}")
            return False
    
    async def get_index_stats(self):
        """获取索引统计信息"""
        try:
            # 获取所有电影键
            keys = self.redis_client.keys("movie:*")
            return {
                "total_movies": len(keys),
                "index_name": self.settings.VECTOR_STORE_INDEX_NAME,
                "index_prefix": self.settings.VECTOR_STORE_INDEX_PREFIX
            }
        except Exception as e:
            logger.error(f"获取索引统计失败: {str(e)}")
            return None

    async def add_movie(self, movie_data: dict):
        """添加电影数据到向量存储"""
        try:
            # 格式化电影文本
            text = self._format_movie_text(movie_data)
            
            # 创建文档
            document = Document(
                text=text,
                metadata=movie_data
            )
            
            # 添加到向量存储
            self.index.insert(document)
            
            # 保存原始数据到Redis
            self.redis_client.hset(
                f"movie:{movie_data['content_code']}",
                mapping=movie_data
            )
            
            return True
        except Exception as e:
            logger.error(f"添加电影数据失败: {str(e)}")
            return False
            
    async def search_movies(self, query: str, top_k: int = 5):
        """搜索电影"""
        try:
            # 执行向量搜索
            query_engine = self.index.as_query_engine(
                similarity_top_k=top_k,
                response_mode="no_text"
            )
            response = query_engine.query(query)
            
            # 处理结果
            results = []
            for node in response.source_nodes:
                # 从Redis获取完整数据
                movie_data = self.redis_client.hgetall(
                    f"movie:{node.metadata['content_code']}"
                )
                if movie_data:
                    results.append(movie_data)
                    
            return results
        except Exception as e:
            logger.error(f"搜索电影失败: {str(e)}")
            return []
            
    async def get_movie_by_id(self, content_code: str):
        """根据ID获取电影数据"""
        try:
            movie_data = self.redis_client.hgetall(f"movie:{content_code}")
            if movie_data:
                return movie_data
            return None
        except Exception as e:
            logger.error(f"获取电影数据失败: {str(e)}")
            return None
            
    def _format_movie_text(self, movie_data: dict) -> str:
        """格式化电影文本，根据字段权重"""
        text_parts = []
        
        # 根据权重重复字段内容
        for field, weight in MOVIE_FIELD_WEIGHTS.items():
            if field in movie_data and movie_data[field]:
                # 重复内容以增加权重
                text_parts.extend([str(movie_data[field])] * weight)
                
        return " ".join(text_parts)

    async def get_all_movies(self):
        """获取所有电影数据"""
        try:
            # 获取所有电影键
            keys = self.redis_client.keys("movie:*")
            if not keys:
                return []
                
            # 获取所有电影数据
            movies = []
            for key in keys:
                movie_data = self.redis_client.hgetall(key)
                if movie_data:
                    movies.append(movie_data)
                    
            return movies
            
        except Exception as e:
            logger.error(f"获取所有电影数据失败: {str(e)}")
            return []

class RedisStorage:
    def __init__(self):
        """初始化 Redis 存储"""
        # 加载环境变量
        load_dotenv()
        
        # 初始化 Redis 客户端
        self.client = redis.Redis(
            host=os.getenv("REDIS_HOST", "localhost"),
            port=int(os.getenv("REDIS_PORT", 6379)),
            db=int(os.getenv("REDIS_DB", 0)),
            decode_responses=True
        )
        
        logger.info("RedisStorage 初始化完成")
        
    async def set_movie(self, content_code: str, movie_data: Dict[str, Any]) -> bool:
        """设置电影缓存"""
        try:
            # 序列化数据
            data = json.dumps(movie_data)
            
            # 设置缓存
            self.client.set(f"movie:{content_code}", data)
            
            logger.info(f"成功设置电影缓存: {content_code}")
            return True
            
        except Exception as e:
            logger.error(f"设置电影缓存失败: {str(e)}")
            return False
            
    async def get_movie(self, content_code: str) -> Optional[Dict[str, Any]]:
        """获取电影缓存"""
        try:
            # 获取缓存
            data = self.client.get(f"movie:{content_code}")
            if not data:
                return None
                
            # 反序列化数据
            return json.loads(data)
            
        except Exception as e:
            logger.error(f"获取电影缓存失败: {str(e)}")
            return None
            
    async def delete_movie(self, content_code: str) -> bool:
        """删除电影缓存"""
        try:
            # 删除缓存
            self.client.delete(f"movie:{content_code}")
            
            logger.info(f"成功删除电影缓存: {content_code}")
            return True
            
        except Exception as e:
            logger.error(f"删除电影缓存失败: {str(e)}")
            return False 