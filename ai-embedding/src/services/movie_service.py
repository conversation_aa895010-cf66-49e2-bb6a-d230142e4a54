from llama_index.core import (
    VectorStoreIndex,
    Document,
    StorageContext,
    Settings
)
from llama_index.vector_stores.chroma import ChromaVectorStore
from config.base import Settings as AppSettings
from config.weights import MOVIE_FIELD_WEIGHTS
from pathlib import Path
from models.schemas import Movie
from typing import List, Dict, Optional, Any
import logging
from models.database import get_db, Movie as MovieDB
from storage.chroma_storage import MovieChromaStorage
from datetime import datetime
import json
import os
import asyncio
from dotenv import load_dotenv
from utils.text_processor import TextProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('movie_service.log')
    ]
)
logger = logging.getLogger(__name__)

class MovieService:
    def __init__(self):
        """初始化电影服务"""
        # 加载环境变量
        load_dotenv()
        
        # 初始化存储
        self.chroma_storage = MovieChromaStorage()
        self.text_processor = TextProcessor()
        
        # 初始化数据库
        self.db = next(get_db())
        
        logger.info("MovieService 初始化完成")
        
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'db'):
            self.db.close()
            
    def _format_movie_text(self, movie_data: dict) -> str:
        """格式化电影文本，根据字段权重"""
        text_parts = []
        for field, weight in MOVIE_FIELD_WEIGHTS.items():
            if field in movie_data and movie_data[field]:
                text_parts.extend([str(movie_data[field])] * weight)
        return " ".join(text_parts)
    
    async def create_movie(self, movie: Movie) -> Movie:
        """创建新电影记录"""
        try:
            # 创建数据库记录
            db_movie = MovieDB(**movie.model_dump())
            self.db.add(db_movie)
            self.db.commit()
            self.db.refresh(db_movie)
            
            # 更新向量存储
            await self.chroma_storage.add_movie(movie)
            
            logger.info(f"成功创建电影: {movie.title}")
            return movie
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建电影失败: {str(e)}")
            raise
    
    async def add_movie(self, movie: Movie) -> bool:
        try:
            movie_dict = movie.model_dump()
            success = await self.chroma_storage.add_movie(movie_dict)
            if success:
                logger.info(f"成功添加电影: {movie.title}")
            else:
                logger.error(f"添加电影失败: {movie.title}")
            return success
        except Exception as e:
            logger.error(f"添加电影失败: {str(e)}")
            return False
    
    async def search_movies(self, text: str, top_k: int = 5) -> List[Movie]:
        """搜索电影"""
        try:
            # 从向量存储搜索
            results = await self.chroma_storage.search_movies(
                query=text,
                top_k=top_k
            )
            
            # 获取详细信息
            movies = []
            for result in results:
                # 将字典转换为 Movie 对象
                movie = Movie(**result)
                movies.append(movie)
                    
            return movies
            
        except Exception as e:
            logger.error(f"搜索电影失败: {str(e)}")
            return []
    
    async def batch_add_movies(self, movies: List[Movie]) -> List[bool]:
        """批量添加电影数据
        
        Args:
            movies: 电影数据列表
            
        Returns:
            成功标志列表
        """
        results = []
        total = len(movies)
        logger.info(f"开始批量添加 {total} 条电影数据")
        
        # 将电影数据转换为字典列表
        movie_dicts = []
        for movie in movies:
            try:
                movie_dict = movie.model_dump()
                movie_dicts.append(movie_dict)
            except Exception as e:
                logger.error(f"转换电影数据失败: {str(e)}")
                results.append(False)
                continue
        
        # 批量处理
        try:
            # 使用 storage 的批量添加功能
            batch_results = self.chroma_storage.batch_add_movies(movie_dicts)
            results.extend(batch_results)
            
            success_count = sum(results)
            logger.info(f"批量添加完成，成功: {success_count}/{total}")
            return results
            
        except Exception as e:
            logger.error(f"批量添加失败: {str(e)}")
            success_count = sum(results)
            logger.info(f"单个处理完成，成功: {success_count}/{total}")
            return results
    
    async def get_movie_by_id(self, content_code: str) -> Optional[Movie]:
        try:
            movie_data = await self.chroma_storage.get_movie_by_id(content_code)
            if movie_data:
                return Movie(**movie_data)
            logger.warning(f"未找到电影: {content_code}")
            return None
        except Exception as e:
            logger.error(f"获取电影失败: {str(e)}")
            return None
    
    async def get_all_movies(self) -> List[dict]:
        """获取所有电影数据"""
        try:
            # 从数据库获取所有电影数据
            movies = await self.chroma_storage.get_all_movies()
            if not movies:
                logger.warning("数据库中没有电影数据")
                return []
                
            # 转换为字典格式
            movie_list = []
            for movie in movies:
                movie_list.append(movie.to_dict())
                
            return movie_list
            
        except Exception as e:
            logger.error(f"获取所有电影数据失败: {str(e)}")
            return [] 
    async def get_movie_index_statistics(self) -> Optional[dict]:
        """获取电影索引的统计信息"""
        try:
            logger.info("正在获取电影索引统计信息...")
            stats = await self.chroma_storage.get_index_stats()
            if stats:
                logger.info(f"获取到索引统计: {stats}")
            else:
                logger.warning("未能获取到索引统计信息或索引为空")
            return stats
        except Exception as e:
            logger.error(f"从服务层获取索引统计失败: {str(e)}")
            return None

    async def clear_movie_index(self) -> bool:
        """清除整个电影索引"""
        try:
            logger.info("服务层请求清除电影索引...")
            success = await self.chroma_storage.clear_index()
            if success:
                logger.info("电影索引已成功清除")
            else:
                logger.warning("电影索引清除操作未成功（可能索引已为空或发生错误）")
            return success
        except Exception as e:
            logger.error(f"从服务层清除索引失败: {str(e)}")
            return False

    async def compare_movie_search_results(self, query: str, top_k: int = 5) -> Optional[dict]:

        """从原始数据库加载所有电影数据并转换为 MovieCreate 对象"""
        
        movies_to_create = []
        db_session = None
        try:
            db_session = next(get_db())
            logger.info("开始从原始数据库加载电影数据...")
            
            db_movies = db_session.query(MovieDB).all()
            total_movies = len(db_movies)
            logger.info(f"从原始数据库加载了 {total_movies} 条电影数据")
            
            if not db_movies:
                return []

            logger.info("开始转换电影数据格式为 MovieCreate 对象...")
            processed_count = 0
            for db_movie_orm in db_movies:
                try:
                    # 从 ORM 对象创建字典
                    movie_data_dict = {}
                    # 获取所有列名
                    all_cols = [column.key for column in db_movie_orm.__table__.columns]
                    for col in all_cols:
                        movie_data_dict[col] = getattr(db_movie_orm, col)
                    
                    # 确保 MovieCreate 所需的核心字段存在
                    if not movie_data_dict.get('content_code') or not movie_data_dict.get('title'):
                        logger.warning(f"跳过原始数据库中的无效数据 (缺少 content_code 或 title): {movie_data_dict}")
                        continue
                    
                    # 使用 MovieCreate 进行验证和创建
                    movie_create_obj = Movie(**movie_data_dict)
                    movies_to_create.append(movie_create_obj)
                    processed_count += 1
                except Exception as e:
                    # 获取电影标题，如果存在的话
                    title_for_log = getattr(db_movie_orm, 'title', '未知标题')
                    logger.error(f"转换数据库电影 '{title_for_log}' 为 MovieCreate 对象失败: {str(e)}")
            
            logger.info(f"成功转换 {processed_count}/{total_movies} 条电影数据为 MovieCreate 对象")
            return movies_to_create
            
        except Exception as e:
            logger.error(f"从原始数据库加载电影数据失败: {str(e)}")
            return []
        finally:
            if db_session:
                db_session.close()
                logger.info("数据库会话已关闭")

            """比较不同搜索方法得到的电影结果"""
            try:
                logger.info(f"服务层请求比较搜索方法，查询: '{query}', top_k: {top_k}")
                comparison_results = await self.chroma_storage.compare_search_methods(query, top_k)
                if comparison_results:
                    logger.info("成功获取到搜索方法比较结果")
                else:
                    logger.warning("未能获取到搜索方法比较结果")
                return comparison_results
            except Exception as e:
                logger.error(f"从服务层比较搜索方法失败: {str(e)}")
                return None    
            
            
    async def load_all_movies_from_source_db(self, page_size: int = 1000) -> List[Movie]:
        """
        从原始数据库分页加载所有电影数据并转换为 Movie 对象
        Args:
            page_size: 每页加载的数据量，默认1000条
        Returns:
            Movie 对象列表
        """
        movies_to_create = []
        db_session = None
        try:
            db_session = next(get_db())
            logger.info("开始从原始数据库分页加载电影数据...")
            total_count = db_session.query(MovieDB).count()
            logger.info(f"数据库中共有 {total_count} 条电影数据")
            if total_count == 0:
                logger.warning("数据库中没有找到电影数据")
                return []
            page = 0
            processed_count = 0
            error_count = 0
            while True:
                offset = page * page_size
                if offset >= total_count:
                    break
                logger.info(f"正在加载第 {page + 1} 页数据 (offset: {offset}, limit: {page_size})...")
                db_movies = db_session.query(MovieDB).offset(offset).limit(page_size).all()
                if not db_movies:
                    break
                for db_movie_orm in db_movies:
                    try:
                        # 详细的调试日志
                        logger.debug(f"处理电影: {db_movie_orm.title}")
                        logger.debug(f"原始数据类型: {type(db_movie_orm)}")
                        
                        movie_data_dict = {}
                        all_cols = [column.key for column in db_movie_orm.__table__.columns]
                        for col in all_cols:
                            value = getattr(db_movie_orm, col)
                            movie_data_dict[col] = value
                        
                        # 额外的数据验证和日志
                        if not movie_data_dict.get('content_code'):
                            logger.warning(f"跳过无效数据 - 缺少 content_code: {movie_data_dict}")
                            continue
                        
                        if not movie_data_dict.get('title'):
                            logger.warning(f"跳过无效数据 - 缺少 title: {movie_data_dict}")
                            continue

                        if not movie_data_dict.get('type'):
                            logger.warning(f"跳过无效数据 - 缺少 type: {movie_data_dict}")
                            continue
                        
                        # 尝试创建 Movie 对象
                        try:
                            movie_create_obj = Movie(**movie_data_dict)
                        except Exception as create_error:
                            logger.error(f"创建 Movie 对象失败: {create_error}")
                            logger.error(f"问题数据: {movie_data_dict}")
                            error_count += 1
                            continue
                        
                        movies_to_create.append(movie_create_obj)
                        processed_count += 1
                    
                    except Exception as e:
                        error_count += 1
                        title_for_log = getattr(db_movie_orm, 'title', '未知标题')
                        logger.error(f"转换数据库电影 '{title_for_log}' 为 Movie 对象失败: {str(e)}")
                        logger.error(f"问题数据详情: {vars(db_movie_orm)}")
                
                logger.info(f"已处理 {processed_count}/{total_count} 条数据 (成功: {processed_count}, 失败: {error_count})")
                page += 1
            
            logger.info(f"数据加载完成: 成功 {processed_count} 条, 失败 {error_count} 条, 总计 {total_count} 条")
            return movies_to_create
        
        except Exception as e:
            logger.error(f"从原始数据库分页加载电影数据失败: {str(e)}")
            return []
        finally:
            if db_session:
                db_session.close()
                logger.info("数据库会话已关闭")

    async def get_movie(self, content_code: str) -> Optional[Movie]:
        """获取电影详情"""
        try:
            logger.info(f"电影详情: {content_code}")
            # 从数据库获取
            db_movie = self.db.query(MovieDB).filter(MovieDB.content_code == content_code).first()
            if not db_movie:
                return None
           

            # 使用 to_dict 方法转换为字典，然后创建 Movie 对象
            movie_dict = db_movie.to_dict()
            logger.info(f"电影详情: {movie_dict}")
            return Movie(**movie_dict)
            
        except Exception as e:
            logger.error(f"获取电影详情失败1: {str(e)}")
            return None
            
    async def update_movie(self, content_code: str, movie: Movie) -> Optional[Movie]:
        """更新电影信息"""
        try:
            # 更新数据库
            db_movie = self.db.query(MovieDB).filter(MovieDB.content_code == content_code).first()
            if not db_movie:
                return None
                
            for key, value in movie.model_dump().items():
                setattr(db_movie, key, value)
                
            self.db.commit()
            self.db.refresh(db_movie)
            
            # 更新向量存储
            await self.chroma_storage.update_movie(movie)
            
            logger.info(f"成功更新电影: {movie.title}")
            return movie
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新电影失败: {str(e)}")
            return None

    async def delete_movie(self, content_code: str) -> bool:
        """删除电影记录"""
        try:
            # 从数据库删除
            db_movie = self.db.query(MovieDB).filter(MovieDB.content_code == content_code).first()
            if not db_movie:
                return False
                
            self.db.delete(db_movie)
            self.db.commit()
            
            # 从向量存储删除
            await self.chroma_storage.delete_movie(content_code)
            
            logger.info(f"成功删除电影: {content_code}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除电影失败: {str(e)}")
            return False

    async def batch_create_movies(self, movies: List[Movie]) -> List[Movie]:
        """批量创建电影记录"""
        created_movies = []
        for movie in movies:
            try:
                created_movie = await self.create_movie(movie)
                created_movies.append(created_movie)
            except Exception as e:
                logger.error(f"批量创建电影失败: {movie.title} - {str(e)}")
                continue
        return created_movies

    async def get_similar_movies(self, content_code: str, top_k: int = 5) -> List[Movie]:
        """获取相似电影
        
        Args:
            content_code: 电影ID
            top_k: 返回的相似电影数量
            
        Returns:
            相似电影列表
        """
        try:
            # 从向量存储获取目标电影
            target_movie = await self.chroma_storage.get_movie_by_id(content_code)
            if not target_movie:
                logger.warning(f"未找到目标电影: {content_code}")
                return []
                
            # 从向量存储搜索相似电影
            results = await self.chroma_storage.search_movies(
                query=self._format_movie_text(target_movie),
                top_k=top_k + 1  # +1 是因为结果中会包含目标电影本身
            )
            
            # 过滤掉目标电影本身
            similar_movies = []
            for result in results:
                if result.get('content_code') != content_code:
                    similar_movies.append(Movie(**result))
                    
            return similar_movies[:top_k]  # 确保返回的数量不超过 top_k
            
        except Exception as e:
            logger.error(f"获取相似电影失败: {str(e)}")
            return []