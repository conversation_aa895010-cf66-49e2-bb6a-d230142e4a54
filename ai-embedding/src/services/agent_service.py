import subprocess
from pydantic import BaseModel
import requests
import logging
from typing import Dict, Any, List, TypedDict, Annotated, Sequence
from langchain_ollama import OllamaLLM
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, END
from services.movie_service import MovieService


logger = logging.getLogger(__name__)

movie_service = MovieService()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
        logging.FileHandler('agent.log')  # 输出到文件
    ]
)

# 定义工具
@tool
def get_weather(city: str) -> str:
    """获取指定城市天气情况"""
    logger.info(f"开始获取天气信息，城市: {city}")
    try:
        resp = requests.get(f"https://wttr.in/{city}?format=3")
        logger.info(f"天气信息获取成功: {city}")
        return resp.text
    except Exception as e:
        logger.error(f"获取天气失败: {str(e)}", exc_info=True)
        return f"获取天气失败: {e}"

@tool
def generate_poem(topic: str) -> str:
    """以指定主题生成现代诗"""
    logger.info(f"开始生成诗歌，主题: {topic}")
    return (
        f'请以"{topic}"为题写一首现代诗：\n'
        f'春风又绿江南岸，{topic}正浓人未还。'
    )

@tool
def open_app(app_name: str) -> str:
    """尝试打开 Mac 系统上的应用程序"""
    logger.info(f"尝试打开应用: {app_name}")
    try:
        subprocess.run(["open", "-a", app_name])
        logger.info(f"应用打开成功: {app_name}")
        return f"已尝试打开应用：{app_name}"
    except Exception as e:
        logger.error(f"打开应用失败: {str(e)}", exc_info=True)
        return f"打开失败：{e}"
@tool
async def query_vod(query: str) -> str:
    """查询视频点播内容，如电影、剧集等"""
    logger.info(f"开始查询视频点播，查询词: {query}")
    try:
        movies = await movie_service.search_movies(query, top_k=5)
        if not movies:
            logger.warning(f"未找到相关视频内容: {query}")
            return "未找到相关视频内容"
        
        result = "找到以下视频内容：\n"
        for movie in movies:
            result += f"- {movie['title']} ({movie['region']})\n"
            if movie['highlight']:
                result += f"  简介：{movie['highlight']}\n"
            if movie['director']:
                result += f"  导演：{movie['director']}\n"
            if movie['actors']:
                result += f"  演员：{movie['actors']}\n"
            if movie['tags']:
                result += f"  标签：{movie['tags']}\n"
            result += "\n"
        logger.info(f"查询完成，找到 {len(movies)} 条结果")
        return movies
    except Exception as e:
        logger.error(f"视频检索失败: {str(e)}", exc_info=True)
        return f"视频检索失败: {e}"
# 定义状态类型
class AgentState(TypedDict):
    messages: Annotated[Sequence[BaseMessage], "对话历史"]
    next: Annotated[str, "下一步操作"]

class AgentService:
    def __init__(self, model: str = "qwen3:4b"):
        """
        初始化Agent服务
        
        :param model: Ollama模型名称
        """
        logger.info(f"初始化 AgentService，使用模型: {model}")
        self.llm = OllamaLLM(
            model=model,
            temperature=0.3,
            format="json"  # 确保返回 JSON 格式
        )
        self.agent = self._build_agent()
        logger.info("AgentService 初始化完成")

  

    def _build_agent(self):
        """构建 LangGraph agent"""
        logger.info("开始构建 Agent")
        # 定义工具
        tools = {
            "query_vod": query_vod,
            "get_weather": get_weather,
            "generate_poem": generate_poem,
            "open_app": open_app
        }
        logger.info(f"已加载 {len(tools)} 个工具")

        # 系统提示
        system_prompt = """你是一个智能助手，可以帮助用户完成各种任务。
你可以使用以下工具：
1. query_vod: 查询视频点播内容，如电影、剧集等
2. get_weather: 获取指定城市的天气情况
3. generate_poem: 以指定主题生成现代诗
4. open_app: 尝试打开 Mac 系统上的应用程序

当用户询问电影相关问题时，你应该使用 query_vod 工具来搜索。
当用户询问天气相关问题时，你应该使用 get_weather 工具来查询。
请用中文回复用户。

你的回复必须是 JSON 格式，包含以下字段：
- action: 要使用的工具名称
- keywords: 传递给工具的参数
"""

        # 定义节点函数
        def should_continue(state: AgentState) -> AgentState:
            """决定是否继续对话"""
            logger.debug(f"检查是否继续对话，当前状态: {state}")
            last_message = state["messages"][-1]
            if isinstance(last_message, AIMessage):
                if "我无法" in last_message.content or "不知道" in last_message.content:
                    logger.info("检测到无法处理的回复，转向 fallback")
                    return {"messages": state["messages"], "next": "fallback"}
            logger.debug("继续正常对话流程")
            return {"messages": state["messages"], "next": "continue"}

        async def call_model(state: AgentState) -> AgentState:
            """调用模型生成回复"""
            logger.debug(f"调用模型生成回复，当前状态: {state}")
            messages = state["messages"]
            try:
                # 添加系统提示
                if len(messages) == 1 and isinstance(messages[0], HumanMessage):
                    messages = [AIMessage(content=system_prompt)] + messages
                
                response = self.llm.invoke(messages)
                logger.info(f"模型原始响应: {response}")
                
                # 解析 JSON 响应
                try:
                    if isinstance(response, str):
                        import json
                        action_data = json.loads(response)
                    else:
                        action_data = response
                    
                    action = action_data.get("action")
                    keywords = action_data.get("keywords")
                    
                    if action in tools:
                        logger.info(f"调用工具: {action}, 参数: {keywords}")
                        # 使用 invoke 方法调用工具
                        tool = tools[action]
                        if action == "query_vod":
                            tool_result = await tool.ainvoke({"query": keywords})
                        elif action == "get_weather":
                            tool_result = await tool.ainvoke({"city": keywords})
                        elif action == "generate_poem":
                            tool_result = await tool.ainvoke({"topic": keywords})
                        elif action == "open_app":
                            tool_result = await tool.ainvoke({"app_name": keywords})
                        else:
                            tool_result = await tool.ainvoke({"input": keywords})
                        
                        response = AIMessage(content=tool_result)
                    else:
                        logger.warning(f"未知的工具: {action}")
                        response = AIMessage(content=f"抱歉，我不支持 {action} 这个操作。")
                except json.JSONDecodeError:
                    logger.error("无法解析 JSON 响应")
                    response = AIMessage(content="抱歉，我的回复格式有误。")
                
                logger.info(f"最终回复: {response.content[:100]}...")
                return {
                    "messages": messages + [response],
                    "next": "continue"
                }
            except Exception as e:
                logger.error(f"模型调用失败: {str(e)}", exc_info=True)
                return {
                    "messages": messages + [AIMessage(content="抱歉，模型调用失败，请稍后重试。")],
                    "next": "fallback"
                }

        def fallback_response(state: AgentState) -> AgentState:
            """生成兜底回复"""
            logger.info("生成兜底回复")
            fallback_msg = "🤖 抱歉，我无法理解你的请求。目前我支持：查询视频点播、获取天气、作诗、打开应用。"
            return {
                "messages": state["messages"] + [AIMessage(content=fallback_msg)],
                "next": "end"
            }

        # 构建图
        logger.info("开始构建工作流图")
        workflow = StateGraph(AgentState)

        # 添加节点
        workflow.add_node("agent", call_model)
        workflow.add_node("fallback", fallback_response)
        workflow.add_node("router", should_continue)
        logger.info("已添加所有节点")

        # 设置边
        workflow.add_edge("agent", "router")
        workflow.add_conditional_edges(
            "router",
            should_continue,
            {
                "continue": "agent",
                "fallback": "fallback",
                "end": END
            }
        )
        workflow.add_edge("fallback", END)
        logger.info("已设置所有边")

        # 设置入口
        workflow.set_entry_point("agent")
        logger.info("已设置入口点")

        # 编译图
        compiled_workflow = workflow.compile()
        logger.info("工作流图编译完成")
        return compiled_workflow

    async def run(self, query: str) -> Dict[str, Any]:
        """
        运行Agent处理查询
        
        :param query: 用户查询
        :return: 处理结果
        """
        logger.info(f"开始处理用户查询: {query}")
        try:
            # 初始化状态
            state = {
                "messages": [HumanMessage(content=query)],
                "next": "agent"
            }
            logger.debug(f"初始化状态: {state}")
            
            # 运行图
            logger.info("开始执行工作流")
            result = await self.agent.ainvoke(state)
            logger.debug(f"工作流执行结果: {result}")
            
            # 获取最后一条消息
            last_message = result["messages"][-1]
            logger.info(f"生成回复: {last_message.content[:100]}...")
            return {"response": last_message.content}
        except Exception as e:
            logger.error(f"处理查询时发生错误: {str(e)}", exc_info=True)
            return {"response": f"❗️出错了：{str(e)}"} 