from pydantic_settings import BaseSettings
from typing import Optional
from pathlib import Path

class BaseConfig(BaseSettings):
    """基础配置类"""
    # 项目基础配置
    PROJECT_NAME: str = "Movie Search API"
    API_V1_STR: str = "/api/v1"
    DEBUG: bool = False
    
    # 路径配置
    BASE_DIR: Path = Path("./")
    DATA_DIR: Path = BASE_DIR / "data"
    CACHE_DIR: Path = BASE_DIR / "cache"
    LOG_DIR: Path = BASE_DIR / "logs"
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"



class Settings(BaseConfig):
    """全局配置类"""
    # 数据库配置
    DB_HOST: str = "localhost"
    DB_PORT: str = "3306"
    DB_USER: str = "root"
    DB_PASSWORD: str = "root"
    DB_NAME: str = "chances_epg_content"
    DB_CHARSET: str = "utf8mb4"
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: str = "6379"
    REDIS_DB: str = "0"
    REDIS_PASSWORD: str = ""
    
    # Ollama配置
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_MODEL: str = "bge-m3"
    OLLAMA_MAX_RETRIES: int = 3
    OLLAMA_RETRY_DELAY: int = 5  # 重试延迟（秒）
    OLLAMA_TIMEOUT: int = 30     # 请求超时时间（秒）
    
    # 向量存储配置
    VECTOR_STORE_INDEX_NAME: str = "movie_index"
    VECTOR_STORE_INDEX_PREFIX: str = "movie:"
    VECTOR_DIM: int = 1024
    
    # Chroma配置
    CHROMA_PERSIST_DIR: Path =   "./data/chroma"  # 将在 __init__ 中设置
    CHROMA_COLLECTION_NAME: str = "movie_index"
    CHROMA_DISTANCE_METRIC: str = "cosine"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 在初始化时设置 CHROMA_PERSIST_DIR
    
    @property
    def db_url(self) -> str:
        """获取数据库连接URL"""
        return f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}?charset={self.DB_CHARSET}"
    
    @property
    def redis_url(self) -> str:
        """获取Redis连接URL"""
        auth = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else "@"
        return f"redis://{auth}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    class Config:
        env_file = ".env"
        case_sensitive = True 
        extra = "allow"


class OllamaConfig(BaseConfig):
    """Ollama模型配置"""
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_MODEL: str = "llama2"
    OLLAMA_BATCH_SIZE: int = 100
    OLLAMA_TIMEOUT: int = 30
    OLLAMA_TEMPERATURE: float = 0.7
    OLLAMA_MAX_TOKENS: int = 2048
    
    # 向量维度配置
    EMBEDDING_DIM: int = 1024         
    class Config:
        env_file = ".env"
        case_sensitive = True