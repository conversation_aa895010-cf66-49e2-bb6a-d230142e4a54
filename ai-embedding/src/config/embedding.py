"""
Embedding 模型配置
"""

from enum import Enum
from typing import Dict, Any

class EmbeddingModelType(str, Enum):
    """Embedding 模型类型"""
    OLLAMA = "ollama"
    BGE = "bge"

# Embedding 模型配置
EMBEDDING_MODELS: Dict[str, Dict[str, Any]] = {
    "bge-3m": {
        "type": EmbeddingModelType.BGE,
        "model_name": "BAAI/bge-3m",
        "dimension": 1024,
        "max_length": 512,
        "device": "cuda",  # 或 "cpu"
        "normalize_embeddings": True,
    },
    "bge-3m-zh": {
        "type": EmbeddingModelType.BGE,
        "model_name": "BAAI/bge-3m-zh",
        "dimension": 1024,
        "max_length": 512,
        "device": "cuda",  # 或 "cpu"
        "normalize_embeddings": True,
    },
    "bge-3m-en": {
        "type": EmbeddingModelType.BGE,
        "model_name": "BAAI/bge-3m-en",
        "dimension": 1024,
        "max_length": 512,
        "device": "cuda",  # 或 "cpu"
        "normalize_embeddings": True,
    },
    "bge-3m-multilingual": {
        "type": EmbeddingModelType.BGE,
        "model_name": "BAAI/bge-3m-multilingual",
        "dimension": 1024,
        "max_length": 512,
        "device": "cuda",  # 或 "cpu"
        "normalize_embeddings": True,
    },
}

# 默认使用的模型
DEFAULT_EMBEDDING_MODEL = "bge-3m-zh"

# Embedding 相关常量
EMBEDDING_CONSTANTS = {
    "batch_size": 32,  # 批处理大小
    "cache_dir": "models",  # 模型缓存目录
    "timeout": 30,  # 请求超时时间（秒）
} 