import os
import sys
from pathlib import Path
import streamlit as st
import asyncio
from typing import List, Optional
import logging
from dotenv import load_dotenv

# 添加项目根目录到 Python 路径


from config import Settings
from services import MovieService
from models.schemas import Movie

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MovieSearchApp:
    # 类级别的 MovieService 实例
    _movie_service = None
    
    @classmethod
    def get_movie_service(cls):
        """获取 MovieService 单例"""
        if cls._movie_service is None:
            cls._movie_service = MovieService()
        return cls._movie_service
    
    def __init__(self):
        """初始化应用"""
        # 使用单例模式获取 MovieService
        self.movie_service = self.get_movie_service()
        self.settings = Settings()
        
    async def search_movies(self, query: str, top_k: int = 5) -> List[Movie]:
        """搜索电影"""
        try:
            results = await self.movie_service.search_movies(query, top_k)
            return results
        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            return []
            
    async def get_similar_movies(self, content_code: str, top_k: int = 5) -> List[Movie]:
        """获取相似电影"""
        try:
            return await self.movie_service.get_similar_movies(content_code, top_k)
        except Exception as e:
            logger.error(f"获取相似电影失败: {str(e)}")
            return []

    def display_movie(self, movie, container, show_similar_button=False):
        """在 Streamlit 中显示电影信息"""
        if isinstance(movie, dict):
            try:
                movie = Movie(**movie)
            except Exception as e:
                container.error(f"结果格式有误: {e}")
                container.json(movie)
                return
                
        with container:
            # 创建标题行，包含相似度分数
            col_title, col_score = st.columns([4, 1])
            with col_title:
                st.subheader(movie.title)
            with col_score:
                if hasattr(movie, 'similarity'):
                    st.metric("相似度", f"{movie.similarity}%")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.write(f"**ID:** {movie.content_code}")
                if movie.director:
                    st.write(f"**导演:** {movie.director}")
                if movie.actors:
                    st.write(f"**演员:** {movie.actors}")
                if movie.region:
                    st.write(f"**地区:** {movie.region}")
                    
            with col2:
                if movie.tags:
                    st.write(f"**标签:** {movie.tags}")
                if movie.highlight:
                    st.write(f"**亮点:** {movie.highlight}")
                if movie.type:
                    st.write(f"**类型:** {movie.type}")
                if movie.poster:
                    st.write(f"**海报:** {movie.poster}")
                    
            if show_similar_button:
                if st.button(f"查找与《{movie.title}》相似的电影", key=f"similar_{movie.content_code}"):
                    # 设置会话状态
                    st.session_state['search_type'] = "相似电影"
                    st.session_state['content_code'] = movie.content_code
                    st.session_state['show_similar'] = True
                    st.session_state['current_movie_id'] = movie.content_code
                    st.rerun()
                    
            st.divider()
            
    async def run(self):
        """运行 Streamlit 应用"""
        st.set_page_config(
            page_title="电影搜索系统",
            page_icon="🎬",
            layout="wide"
        )
        
        st.title("🎬 电影搜索系统")
        
        # 初始化会话状态
        if 'search_type' not in st.session_state:
            st.session_state['search_type'] = "普通搜索"
        if 'content_code' not in st.session_state:
            st.session_state['content_code'] = None
        if 'show_similar' not in st.session_state:
            st.session_state['show_similar'] = False
        if 'current_movie_id' not in st.session_state:
            st.session_state['current_movie_id'] = None
        if 'show_management' not in st.session_state:
            st.session_state['show_management'] = False
            
        # 创建顶部导航栏
        col1, col2 = st.columns([4, 1])
        with col1:
            # 搜索选项
            search_type = st.radio(
                "选择搜索方式",
                ["普通搜索", "比较搜索", "相似电影"],
                horizontal=True,
                key="search_type_radio",
                index=["普通搜索", "比较搜索", "相似电影"].index(st.session_state['search_type'])
            )
        with col2:
            # 添加管理按钮
            if st.button("电影管理", type="secondary"):
                st.session_state['show_management'] = not st.session_state['show_management']
                st.rerun()
            
        # 使用表单支持回车搜索
        with st.form("search_form"):
            # 搜索参数
            col1, col2 = st.columns([3, 1])
            with col1:
                if search_type == "相似电影":
                    content_code = st.text_input(
                        "请输入电影ID",
                        placeholder="例如：movie_123",
                        value=st.session_state['content_code'] or ""
                    )
                else:
                    query = st.text_input("请输入搜索关键词", placeholder="例如：周星驰的喜剧电影")
            with col2:
                top_k = st.number_input("返回结果数量", min_value=1, max_value=100, value=5)
                
            # 搜索按钮
            submitted = st.form_submit_button("搜索", type="primary")
            
        if submitted:
            with st.spinner("正在搜索..."):
                if search_type == "普通搜索":
                    if not query:
                        st.warning("请输入搜索关键词")
                        return
                        
                    results = await self.search_movies(query, top_k)
                    
                    if not results:
                        st.info("未找到相关电影")
                        return
                        
                    st.success(f"找到 {len(results)} 条结果")
                    for movie in results:
                        self.display_movie(movie, st.container(), show_similar_button=True)
                        
                elif search_type == "比较搜索":
                    if not query:
                        st.warning("请输入搜索关键词")
                        return
                        
                    comparison = await self.movie_service.compare_movie_search_results(query, top_k)
                    if comparison:
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.subheader("向量搜索结果")
                            vector_info = comparison.get('vector_search', {})
                            st.info(f"找到 {vector_info.get('count', 0)} 条结果")
                            for movie in vector_info.get('results', []):
                                self.display_movie(movie, st.container(), show_similar_button=True)
                                
                        with col2:
                            st.subheader("LLM搜索结果")
                            llm_info = comparison.get('llm_search', {})
                            st.info(f"找到 {llm_info.get('count', 0)} 条结果")
                            for movie in llm_info.get('results', []):
                                self.display_movie(movie, st.container(), show_similar_button=True)
                                
                else:  # 相似电影
                    if not content_code:
                        st.warning("请输入电影ID")
                        return
                        
                    similar_movies = await self.get_similar_movies(content_code, top_k)
                    
                    if not similar_movies:
                        st.info("未找到相似电影")
                        return
                        
                    st.success(f"找到 {len(similar_movies)} 部相似电影")
                    for movie in similar_movies:
                        self.display_movie(movie, st.container(), show_similar_button=True)
                                
        # 显示电影管理界面
        if st.session_state['show_management']:
            st.subheader("电影管理")
            
            # 创建两个标签页
            tab1, tab2 = st.tabs(["添加电影", "删除电影"])
            
            with tab1:
                st.write("添加新电影")
                with st.form("add_movie_form"):
                    # 基本信息
                    content_code = st.text_input("电影ID", placeholder="例如：movie_123")
                    title = st.text_input("电影名称", placeholder="例如：肖申克的救赎")
                    
                    # 详细信息
                    col1, col2 = st.columns(2)
                    with col1:
                        director = st.text_input("导演", placeholder="例如：弗兰克·德拉邦特")
                        actors = st.text_input("演员", placeholder="例如：蒂姆·罗宾斯,摩根·弗里曼")
                        region = st.text_input("地区", placeholder="例如：美国")
                    with col2:
                        movie_type = st.text_input("类型", placeholder="例如：剧情,犯罪")
                        tags = st.text_input("标签", placeholder="例如：经典,励志")
                        year = st.number_input("年份", min_value=1900, max_value=2024, value=2024)
                    
                    # 其他信息
                    highlight = st.text_area("亮点", placeholder="例如：IMDb评分最高的电影")
                    summary = st.text_area("简介", placeholder="例如：两个被囚禁的人找到慰藉...")
                    poster = st.text_input("海报URL", placeholder="例如：https://example.com/poster.jpg")
                    
                    # 提交按钮
                    submitted = st.form_submit_button("添加电影", type="primary")
                    
                    if submitted:
                        if not content_code or not title:
                            st.error("电影ID和名称是必填项！")
                        else:
                            try:
                                # 创建电影对象
                                movie = Movie(
                                    content_code=content_code,
                                    title=title,
                                    director=director or None,
                                    actors=actors or None,
                                    region=region or None,
                                    type=movie_type or None,
                                    tags=tags or None,
                                    year=year,
                                    highlight=highlight or None,
                                    summary=summary or None,
                                    poster=poster or None
                                )
                                
                                # 添加电影
                                success = await self.movie_service.add_movie(movie)
                                if success:
                                    st.success(f"成功添加电影：{title}")
                                    # 清空表单
                                    st.rerun()
                                else:
                                    st.error("添加电影失败，请检查电影ID是否已存在")
                            except Exception as e:
                                st.error(f"添加电影失败：{str(e)}")
            
            with tab2:
                st.write("删除电影")
                # 搜索表单
                with st.form("delete_movie_form"):
                    # 搜索要删除的电影
                    search_query = st.text_input("搜索电影", placeholder="输入电影名称或ID")
                    # 提交按钮
                    submitted = st.form_submit_button("搜索", type="primary")
                
                # 在表单外部处理搜索结果和删除操作
                if submitted and search_query:
                    results = await self.search_movies(search_query, top_k=5)
                    if results:
                        st.write("搜索结果：")
                        for movie in results:
                            col1, col2 = st.columns([4, 1])
                            with col1:
                                st.write(f"**{movie.title}** (ID: {movie.content_code})")
                            with col2:
                                # 使用会话状态来跟踪要删除的电影
                                if st.button("删除", key=f"delete_{movie.content_code}"):
                                    try:
                                        success = await self.movie_service.delete_movie(movie.content_code)
                                        if success:
                                            st.success(f"成功删除电影：{movie.title}")
                                            st.rerun()
                                        else:
                                            st.error("删除电影失败")
                                    except Exception as e:
                                        st.error(f"删除电影失败：{str(e)}")
                    else:
                        st.info("未找到相关电影")

        # 添加使用说明
        with st.expander("使用说明"):
            st.markdown("""
            ### 搜索功能说明
            1. **普通搜索**：使用向量搜索快速查找相关电影
            2. **比较搜索**：同时展示向量搜索和 LLM 搜索的结果，方便比较两种搜索方式的差异
            3. **相似电影**：根据电影ID查找内容相似的电影
            
            ### 搜索技巧
            - 可以使用电影名称、导演、演员、类型等关键词
            - 支持自然语言查询，如"周星驰的喜剧电影"
            - 可以调整返回结果数量（1-20条）
            - 在搜索结果中点击"查找相似电影"按钮可以查看与当前电影相似的其他电影
            """)

def main():
    # 加载环境变量
    load_dotenv()
    
    # 创建并运行应用
    app = MovieSearchApp()
    asyncio.run(app.run())

if __name__ == "__main__":
    main() 