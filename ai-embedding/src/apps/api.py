"""
FastAPI 应用入口
"""

from fastapi import FastAPI
from services.agent_service import AgentService,query_vod
from pydantic import BaseModel
from services.movie_service import MovieService, SearchQuery
import logging


movie_service = MovieService()
# 创建 FastAPI 应用
app = FastAPI(title="Agent API", description="智能代理 API 服务")

# 创建 Agent 服务实例
agent_service = AgentService()

logger = logging.getLogger(__name__)

class ChatRequest(BaseModel):
    """聊天请求模型"""
    query: str
    max_results: int = 10

@app.post("/chat")
async def chat_endpoint(req: ChatRequest):
    """聊天接口"""
    try:
        response = await agent_service.run(req.query)
        return response
    except Exception as e:
        return {"error": str(e)}

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy"} 

@app.post("/vod")
async def vod_endpoint(query: ChatRequest):
    """电影接口"""
    try:
        # 使用字典来存储分组结果
        grouped_movies = {}
        search_query = SearchQuery(text=query.query, max_results=query.max_results)
        results = await movie_service.search_movies(query.query,query.max_results)
        
        for movie in results:
            try:
                movie_dict = {
                    "title": str(movie.title),
                    "region": str(movie.region or ''),
                    "highlight": str(movie.highlight or ''),
                    "director": str(movie.director or ''),
                    "actors": str(movie.actors or ''),
                    "level_tags": str(movie.tags or ''),
                    "genre": str(movie.type or ''),
                    "score": str(movie.score or ''),
                    "poster": str(movie.poster or ''),
                    "code": str(movie.content_code),
                    "summary": str(movie.summary or ''),
                }
                
                # 获取电影类型，如果没有则归类为"其他"
                genre = movie_dict['genre'] if movie_dict['genre'] else "其他"
                
                # 如果该类型不存在，则创建新列表
                if genre not in grouped_movies:
                    grouped_movies[genre] = []
                    
                # 将电影添加到对应类型的列表中
                grouped_movies[genre].append(movie_dict)
                
            except Exception as e:
                logger.error(f"处理电影数据失败: {str(e)}, 电影数据: {movie}")
                continue
                
        return {"result": grouped_movies, 'code': 200}
    except Exception as e:
        logger.error(f"搜索电影失败: {str(e)}")
        return {"error": str(e), 'code': 500}