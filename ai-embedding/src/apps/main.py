"""
Agent 模块主入口

提供命令行和 FastAPI 两种使用方式
"""

import os
import sys
import argparse
from typing import Dict, Any
from fastapi import FastAPI, Request
from pydantic import BaseModel
import uvicorn
import asyncio

from services.agent_service import AgentService
from utils.json_response import create_json_response, error_response

class ChatRequest(BaseModel):
    """聊天请求模型"""
    query: str

def create_app(model: str = "qwen3:4b") -> FastAPI:
    """创建 FastAPI 应用实例"""
    agent_service = AgentService(model=model)
    app = FastAPI(title="Agent API", description="智能代理 API 服务")
    
    @app.post("/chat")
    async def chat_endpoint(req: ChatRequest):
        """聊天接口"""
        try:
            response = await agent_service.run(req.query)
            return response
        except Exception as e:
            return {"error": str(e)}

    @app.get("/health")
    async def health_check():
        """健康检查接口"""
        return {"status": "healthy"}
    
    return app

async def run_cli(model: str = "qwen3:4b"):
    """运行命令行模式"""
    agent_service = AgentService(model=model)
    print("🤖 Agent 已启动，输入 'exit' 或 'quit' 退出")
    while True:
        user_input = input("👤 你: ")
        if user_input.lower() in ["exit", "quit"]:
            break
        response = await agent_service.run(user_input)
        print(f"🤖 {response.get('response', '无法处理')}\n")

def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description="Agent 模块")
    parser.add_argument("--cli", action="store_true", help="使用命令行模式")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--model", type=str, default="qwen3:4b", help="Ollama 模型名称")
    args = parser.parse_args()

    if args.cli:
        asyncio.run(run_cli(args.model))
    else:
        print(f"🚀 启动服务器，端口: {args.port}")
        uvicorn.run(
            "apps.main:create_app",
            host="0.0.0.0",
            port=args.port,
            reload=True,
            factory=True
        )

if __name__ == "__main__":
    main() 