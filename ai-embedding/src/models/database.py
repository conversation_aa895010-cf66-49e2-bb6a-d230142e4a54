from sqlalchemy import Column, Integer, String, Text, DateTime, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
from config import  MOVIE_FIELD_WEIGHTS
from config import Settings
from .schemas import Movie

Base = declarative_base()

# 创建数据库连接
db_settings = Settings()
engine = create_engine(db_settings.db_url)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

class MovieDB(Base):
    """电影数据库模型"""
    __tablename__ = "epg_content_vod"
    content_code = Column(String(50),  primary_key=True, unique=True, nullable=False, index=True)
    title = Column(String(200), nullable=False)
    type = Column(String(50),nullable=True)
    director = Column(String(100))
    score = Column(String(10))
    region = Column(String(100))
    actors = Column(String(500))
    tags = Column(String(200))
    poster = Column(String(200))
    year = Column(Integer)
    highlight = Column(Text)    
    summary = Column(Text)
    create_time = Column(DateTime, default=datetime.now)
    def to_dict(self):
        """转换为字典"""
        return {
            "content_code": str(self.content_code),
            "title": self.title,
            "type": self.type,
            "director": self.director,
            "score": self.score,
            "region": self.region,
            "actors": self.actors,
            "tags": self.tags,
            "year": self.year,
            "highlight": self.highlight,
            "summary": self.summary,
            "create_time": self.create_time,
            "poster": self.poster
        }
    def to_movie(self) -> Movie:
        """转换为 Movie 对象"""
        return Movie(
            content_code=str(self.content_code),
            title=self.title,
            type=self.type,
            director=self.director,
            score=self.score,
            region=self.region,
            actors=self.actors,
            tags=self.tags,
            year=self.year,
            highlight=self.highlight,
            summary=self.summary,
            poster=self.poster
        )

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close() 