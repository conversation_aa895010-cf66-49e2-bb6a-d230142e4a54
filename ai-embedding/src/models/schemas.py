from pydantic import BaseModel
from typing import Optional, List

class Movie(BaseModel):
    """电影模型"""
    content_code: str
    title: str
    region: Optional[str] = None
    director: Optional[str] = None
    actors: Optional[str] = None
    tags: Optional[str] = None
    highlight: Optional[str] = None
    summary: Optional[str] = None
    type: Optional[str] = None
    score: Optional[str] = None
    poster: Optional[str] = None
    year: Optional[int] = None
    similarity: Optional[float] = None  # 添加相似度字段

    class Config:
        from_attributes = True  # 允许从ORM模型创建

class SearchQuery(BaseModel):
    """搜索查询模型"""
    text: str
    max_results: int = 5

class SearchResponse(BaseModel):
    """搜索响应模型"""
    results: List[Movie]
    total: int