import json
from typing import Dict, Any

def create_json_response(data: Dict[str, Any], status: int = 200) -> Dict[str, Any]:
    """
    创建标准化的JSON响应
    
    :param data: 响应数据
    :param status: HTTP状态码
    :return: 格式化的响应字典
    """
    return {
        "statusCode": status,
        "body": json.dumps(data, ensure_ascii=False),
        "headers": {
            "Content-Type": "application/json; charset=utf-8"
        }
    }

def error_response(message: str, status: int = 400) -> Dict[str, Any]:
    """
    创建错误响应
    
    :param message: 错误信息
    :param status: HTTP状态码
    :return: 格式化的错误响应
    """
    return create_json_response({"error": message}, status) 