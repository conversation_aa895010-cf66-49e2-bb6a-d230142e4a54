import re
import logging
from typing import List, Dict, Optional
from config.weights import MOVIE_FIELD_WEIGHTS

logger = logging.getLogger(__name__)

class TextProcessor:
    """文本处理器"""
    
    def __init__(self):
        """初始化文本处理器"""
        self.logger = logging.getLogger(__name__)
        
    def clean_text(self, text: str) -> str:
        """
        清理文本
        
        Args:
            text: 输入文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return ""
            
        # 移除多余空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', text)
        return text.strip()
        
    def format_movie_text(self, movie_data: Dict) -> str:
        """
        格式化电影文本，根据字段权重
        
        Args:
            movie_data: 电影数据字典
            
        Returns:
            格式化后的文本
        """
        if not movie_data:
            return ""
            
        text_parts = []
        
        # 根据权重重复添加字段内容
        for field, weight in MOVIE_FIELD_WEIGHTS.items():
            if field in movie_data and movie_data[field]:
                # 清理字段内容
                field_text = self.clean_text(str(movie_data[field]))
                if field_text:
                    # 根据权重重复添加字段内容
                    text_parts.extend([field_text] * weight)
                    
        # 使用空格连接所有文本部分
        return " ".join(text_parts)
        
    def extract_keywords(self, text: str) -> List[str]:
        """
        提取关键词
        
        Args:
            text: 输入文本
            
        Returns:
            关键词列表
        """
        if not text:
            return []
            
        # 清理文本
        text = self.clean_text(text)
        
        # 分词（简单实现，可以替换为更复杂的分词算法）
        words = text.split()
        
        # 过滤停用词（简单实现，可以扩展停用词列表）
        stop_words = {'的', '了', '和', '是', '在', '我', '有', '这', '个', '们', '中', '为', '以', '及', '与', '或', '但', '而', '且', '也', '都', '又', '并', '对', '等', '着', '到', '让', '使', '被', '由', '从', '向', '往', '来', '去', '上', '下', '左', '右', '前', '后', '里', '外', '内', '间', '旁', '边', '面', '方', '向', '位', '处', '所', '时', '分', '秒', '年', '月', '日', '周', '期', '季', '度', '次', '回', '遍', '趟', '场', '次', '回', '遍', '趟', '场', '次', '回', '遍', '趟', '场'}
        keywords = [word for word in words if word not in stop_words]
        
        return keywords 