#!/bin/bash

# 设置项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 设置 PYTHONPATH
export PYTHONPATH="${PROJECT_ROOT}/src"

echo "PYTHONPATH: $PYTHONPATH"

# 激活虚拟环境
source .venv/bin/activate

# 检查命令行参数
if [ "$1" = "api" ]; then
    echo "启动 FastAPI 应用..."
    uvicorn apps.api:app --host 0.0.0.0 --port 8000 --reload --workers 4
elif [ "$1" = "web" ]; then
    echo "启动 Streamlit 应用..."
    streamlit run src/apps/movie_search_app.py
else
    echo "请指定启动模式："
    echo "  ./start_app.sh api  - 启动 FastAPI 后端服务"
    echo "  ./start_app.sh web  - 启动 Streamlit Web 界面"
    exit 1
fi 